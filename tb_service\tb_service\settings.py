"""
Django's settings for tb_service project.

Generated by 'django-admin startproject' using Django 3.2.

For more information on this file, see
https://docs.djangoproject.com/en/3.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/3.2/ref/settings/
"""

import json
import os
import sys
from pathlib import Path

import environ
import pymysql
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

pymysql.install_as_MySQLdb()

# Initialise environment variables
env = environ.Env()

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

environ.Env.read_env(os.path.join(BASE_DIR, '.env'))

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/3.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = env('SECRET_KEY')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = False

HEADER = {'Content-type': 'application/json', 'Accept': '*/*'}

LOGIN_CREDENTIALS = json.dumps({"username": env('TB_USERNAME'), "password": env('TB_PASSWORD')})

session = requests.Session()
retry = Retry(total=5, connect=5, backoff_factor=0.5, status_forcelist=[500, 502, 503, 504])
adapter = HTTPAdapter(max_retries=retry, pool_maxsize=100, pool_connections=100)
session.mount('http://', adapter)
session.mount('https://', adapter)

# CSML Data forwarding URLs
CSML_LOGIN_URL = "https://bi.smartkochi.in:8443/auth/realms/schnellenergy/protocol/openid-connect/token"
CSML_LAMP_FAILURE_URL = "https://bi.smartkochi.in:8443/apiman-gateway/FLUENTGRID/Lampfailure/1.0?apikey=1c7b2899-167f-443b-8b33-64885e6cac4a"
# lamp_fail_url = "https://bi.smartkochi.in:8443/ccc/lampFailureData"
kochin_user_data = "username=SchnellEnergyUser&password=sch%24ell@n@r%26yu%24er&grant_type=password&client_id=SchnellEnergy"

BASE_URL = env('BASE_URL')
ILM_SERVER_ATTRIBUTES = "testResults,PDI,state,condition,dimmable,active,wardName,zoneName,region,qrCount"
LUME_PRINT_QR_REQUIRED_ROLE = "production manager"
ILM_ATTRIBUTE_TYPE_CONVERT_BOOL = ["dimmable", "PDI", "brightness_100", "brightness_70", "brightness_50",
                                   "brightness_30", "brightness_0", "flash", "rtc", "active"]
ILM_ATTRIBUTE_TYPE_CONVERT_INT = ["qrCount", "error_count", "start_ts", "end_ts", "lastActivityTime", "installedOn",
                                  "lampWatts", "armCount", "lampWattage"]
ILM_ATTRIBUTE_TYPE_CONVERT_DOUBLE = ["latitude", "longitude", "accuracy", "slatitude", "slongitude"]
GW_SERVER_ATTRIBUTES = "active,state,condition,wardName,zoneName,region"
ENTITY_TYPE = ["DEVICE", "ASSET"]
ENTITY_TYPE_ASSET = "ASSET"
ENTITY_TYPE_DEVICE = "DEVICE"
ATTRIBUTE_SCOPES = ["CLIENT_SCOPE", "SERVER_SCOPE", "SHARED_SCOPE"]
DEVICE_TYPES = ("gw", "ilm", "ilm-4g", "ccms", "nic", "smslc")
RELATION_TYPES = ["Contains", "ControlledBy", "LitBy", "Powers", "Routes", "Mounts", "CanAccess"]
ILM_TEST_QUERY_PARAMS = ["jigNumber", "deviceId", "action"]
GW_ENTITY_ID_KEY = ["deviceId"]
ENTITY_RELATIONS = ["FROM", "TO"]
GW_DISPATCH_QUERY_PARAMS = ["panelId", "gwType", "ebMeterNo", "phase", "simNo"]
GW_ASSET_TYPES = ("ccms", "hub", "smslc")
ILM_ASSET_TYPES = ["lamp", "lightPoint", "region", "zone", "ward", "pole", "switchPoint"]
LIGHT_POINT_INSTALLATION_ATTRIBUTES = ["latitude", "longitude", "accuracy", "landmark", "wardName", "zoneName",
                                       "region", "installedOn", "installedBy", "lampWatts"]
ILM_INSTALLATION_ATTRIBUTES = ["state", "condition", "landmark", "latitude", "longitude", "zoneName", "region",
                               "wardName", "installedOn", "installedBy"]
CCMS_INSTALLATION_ATTRIBUTES = ["slatitude", "slongitude", "accuracy", "location", "wardName", "zoneName", "region",
                                "installedOn", "installedBy"]
CCMS_EBMETER_ATTRIBUTES = {"ebMeterNo": "name", "meterReadingOffset": "meterReadingOffset",
                           "ebMeterReplacedBy": "replacedBy", "ebMeterReplacedOn": "replacedOn"}
GW_INSTALLATION_ATTRIBUTES = ["state", "condition", "location", "wardName", "zoneName", "region", "installedOn",
                              "installedBy"]
POLE_INSTALLATION_ATTRIBUTES = ["type", "lampProfiles", "name", "discomPoleNumber", "height", "condition", "span", "clampDimension",
                                "vehicleAccessAvailable", "connection", "earthingRequired", "controlWireStatus", "armDetails", "accuracy",
                                "manualSwitchControl", "remarks", "signalStrength", "region", "zoneName", "sector", "wardName", "installedBy", "installedOn",
                                "roadCategory", "roadWidth", "incomingTransmissionLine", "incomingTransmissionType", "bracketMountingHeight",
                                "state", "locationDetails", "latitude", "longitude", "location", "armCount", "auditImg", "assetType", "roadType"]
LAMP_INSTALLATION_ATTRIBUTES = ["lampWatts", "manufacturer", "lampType", "year", "wardName", "installedOn",
                                "installedBy", "dimmable", "state", "condition"]
SWITCH_POINT_INSTALLATION_ATTRIBUTES = ["switchPointNumber", "switchPointType", "panelId", "rrNumber", "meter", "connectedLoad", "roadType", "assetType", 
                                        "workingCondition", "earthingCondition", "customerId", "wardId", "switchPointId", "remarks", "region",
                                        "zoneName", "wardName", "installedBy", "installedOn", "state", "locationDetails", "meterDetails", 
                                        "latitude", "longitude", "location", "accuracy", "roadCategory", "roadWidth", "vehicleAccess"]
DEVICE_FAILURE_STATUS_HANDLER = [200, 401]
COMMON_PARAMS = "lastActivityTime"
LIGHT_POINT_ASSET_FORMAT = "LP-{asset_name}"
LAMP_ASSET_FORMAT = "LMP-{asset_name}"
AVAILABLE_STATES = ["TESTABLE", "TESTED", "INSTALLABLE", "INSTALLED"]
AVAILABLE_CONDITION = ["NEW", "SERVICE", "SCRAPPED"]
REPLACE_AND_REMOVE_DEVICE_STATE_AND_CONDITION = {"state": "INSTALLABLE"}
LAMP_INSTALLED_STATE_CONDITION = {"state": "INSTALLED", "condition": "NEW"}
INSTALLED_STATE = {"state": "INSTALLED"}
OWNER_TYPE = ["CUSTOMER", "TENANT", "USER"]
OWNER_TYPE_CUSTOMER = "CUSTOMER"
LP_SERVER_ATTRIBUTES = ["accuracy", "lamp"]
DEVICE_FIND_QUERY_ATTRIBUTE_RESPONSE_KEYS = ["state", "condition", "region", "wardName", "zoneName", "location",
                                             "landmark", "latitude", "longitude", "slatitude", "slongitude",
                                             "commissioned", "lastActivityTime", "active", "lampWatts", "accuracy",
                                             "installedOn", "installedBy"]
DEVICE_FIND_QUERY_ENTITY_RESPONSE_KEYS = {"name": "name", "label": "label", "type": "type", "device_id": "id"}
PROJECT_WISE_WATTAGE = "lampWattage"

# mysql db for device onboard

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': env('DB_NAME'),
        'USER': env('DB_USER'),
        'PASSWORD': env('DB_PASSWORD'),
        'HOST': env('DB_HOST'),  # Usually 'localhost' for local development
        'PORT': env('DB_PORT'),  # Default MySQL port is 3306
    }
}

AWS_BUCKET_TYPE = env('AWS_BUCKET_TYPE')
AWS_BUCKET_NAME = env('AWS_BUCKET_NAME')
AWS_BUCKET_REGION = env('AWS_BUCKET_REGION')
AWS_BUCKET_ACCESS_KEY = env('AWS_BUCKET_ACCESS_KEY')
AWS_BUCKET_SECRET_KEY = env('AWS_BUCKET_SECRET_KEY')
AWS_BUCKET_GRIEVANCE_FOLDER_NAME = env('AWS_BUCKET_GRIEVANCE_FOLDER_NAME')
AWS_BUCKET_LUMINATOR_FOLDER_NAME = env('AWS_BUCKET_LUMINATOR_FOLDER_NAME')
AWS_FILE_PATH = 'http://' + AWS_BUCKET_NAME + '.' + AWS_BUCKET_TYPE + '.' + AWS_BUCKET_REGION + '.' + 'amazonaws.com'
AWS_BUCKET_PPE_IMAGE_FOLDER_NAME = env('AWS_BUCKET_PPE_IMAGE_FOLDER_NAME')

# GCP Integration Table details
SERVICE_ACCOUNT_KEY_PATH = env('SERVICE_ACCOUNT_KEY_PATH')
PUBSUB_POLE_TOPIC_PATH = env('PUBSUB_POLE_TOPIC_PATH')
PUBSUB_LAMP_TOPIC_PATH = env('PUBSUB_LAMP_TOPIC_PATH')
PUBSUB_POLE_RELOCATION_TOPIC_PATH = env('PUBSUB_POLE_RELOCATION_TOPIC_PATH')
PUBSUB_ACTIVITY_LOG_TOPIC_PATH = env('PUBSUB_ACTIVITY_LOG_TOPIC_PATH')
PUBSUB_IAM_USER_DATA_TOPIC_PATH = env('PUBSUB_IAM_USER_DATA_TOPIC_PATH')

# ILM device install service aws credentials in Testing env
AWS_INSTALL_SERVICE_SQS_URL = env('AWS_INSTALL_SERVICE_SQS_URL')
AWS_INSTALL_SERVICE_REGION = env('AWS_INSTALL_SERVICE_REGION')
AWS_INSTALL_SERVICE_ACCESS_KEY = env('AWS_INSTALL_SERVICE_ACCESS_KEY')
AWS_INSTALL_SERVICE_SECRET_KEY = env('AWS_INSTALL_SERVICE_SECRET_KEY')

# Logging configurations

LOG_LEVEL = os.environ.get('LOGLEVEL', 'info').upper()
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '[%(levelname)s] %(asctime)s <%(name)s.%(module)s: %(lineno)s>    %(message)s',
            'datefmt': '%Y-%m-%d %H:%M:%S'
        },
        'simple': {
            'format': '%(levelname)s %(message)s'
        }
    },
    'handlers': {
        'file': {
            'level': LOG_LEVEL,
            'class': 'logging.handlers.TimedRotatingFileHandler',
            'filename': env('LOG_FILE_NAME'),
            'when': 'W0',
            'backupCount': 100,
            'formatter': 'verbose',
        },
        'console': {
            'level': LOG_LEVEL,
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
            'stream': sys.stdout
        }
    },
    'root': {
        'handlers': ['file'],
        'level': LOG_LEVEL,
    }
}

ALLOWED_HOSTS = ['*']

# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    "corsheaders",
    'lume',
    'onboarder'
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    "corsheaders.middleware.CorsMiddleware",
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    # 'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'tb_service.middleware.LogRequestMiddleware'
]

ROOT_URLCONF = 'tb_service.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'tb_service.wsgi.application'

# Password validation
# https://docs.djangoproject.com/en/3.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

CORS_ALLOWED_ORIGINS = [
    "http://iotpro.io",
    "https://iotpro.io",
    "http://schnelliot.in",
    "https://schnelliot.in",
    "http://tbce.iotpro.io:8080",
    "http://prod.schnelliot.in",
    "https://prod.schnelliot.in",
    "http://prod.schnelliot.in:8080",
    "http://192.168.1.206:8080",
    "https://signify.iotpro.io",
    "https://customergrievance-9acd9.web.app"
]
CORS_ORIGIN_WHITELIST = [
    'http://iotpro.io',
    'https://iotpro.io',
    'http://schnelliot.in',
    'https://schnelliot.in',
    "http://tbce.iotpro.io:8080",
    "http://prod.schnelliot.in",
    "https://prod.schnelliot.in",
    "http://prod.schnelliot.in:8080",
    "http://192.168.1.206:8080",
    "https://signify.iotpro.io",
    "https://customergrievance-9acd9.web.app"
]
CORS_ALLOW_METHODS = ['DELETE', 'GET', 'OPTIONS', 'PATCH', 'POST', 'PUT']
CORS_ALLOW_HEADERS = [
    'accept', 'accept-encoding', 'authorization',
    'content-type', 'dnt', 'origin', 'token',
    'user-agent', 'x-csrftoken', 'x-requested-with',
    'Accept-Language', 'Content-Language', 'X-Custom-Header'
]

CORS_ALLOW_CREDENTIALS = True

# Internationalization
# https://docs.djangoproject.com/en/3.2/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'Asia/Kolkata'

USE_I18N = True

USE_L10N = True

USE_TZ = True

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/3.2/howto/static-files/

STATIC_URL = '/static/'
MEDIA_ROOT = os.path.abspath('Media')
MEDIA_URL = 'Media/'

# Default primary key field type
# https://docs.djangoproject.com/en/3.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

GREYTHR_HOST =  env('GREYTHR_HOST')
GREYTHR_API_ID = env('GREYTHR_API_ID')
GREYTHR_API_ENDPOINT = env('GREYTHR_API_ENDPOINT')
GREYTHR_PEM_FILEPATH = env('GREYTHR_PEM_FILEPATH')
DATA_UPLOAD_MAX_MEMORY_SIZE = 10 * 1024 * 1024 # 10 MB
