import secrets
import time
import hashlib
from celery import shared_task
from django.core.mail import send_mail
from django.conf import settings

# In-memory OTP store (for demo; not for production)
otp_store = {}

@shared_task
def run_script_task():
    import subprocess
    subprocess.run(['python', 'D:/schnelliot/schnelliot-api/script.py'])

def hash_otp(otp):
    return hashlib.sha256(otp.encode()).hexdigest()

def generate_otp(email):
    otp = '{:06d}'.format(secrets.randbelow(1000000))
    otp_hash = hash_otp(otp)
    # Store hash with timestamp (valid for 5 min)
    otp_store[email] = {'otp_hash': otp_hash, 'ts': time.time()}
    return otp, otp_hash

def validate_otp(email, otp_hash):
    entry = otp_store.get(email)
    if not entry:
        return False
    if time.time() - entry['ts'] > 300:
        del otp_store[email]
        return False
    if entry['otp_hash'] == otp_hash:
        del otp_store[email]
        return True
    return False
