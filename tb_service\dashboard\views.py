from tb_service.settings import *
from .otp_utils import generate_otp, validate_otp, run_script_task, otp_store, hash_otp
from django.core.mail import send_mail
from django.http import HttpResponse, JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.conf import settings
import json
from email.message import EmailMessage
import smtplib

sender_email = "<EMAIL>"
receiver_email = ["<EMAIL>"]
email_password = "ynrn tcrd tlkx dmke"  # Use App Password if using Gmail with 2FA

class RequestResponseHandler:
    def __init__(self):
        self.response = {"status": 200}

    def update_service_response(self, response) -> None:
        self.response = response

    def get_service_response(self) -> json:
        return json.dumps(self.response)

    def update_error_response(self, error: str) -> None:
        previous_response = json.loads(self.get_service_response())
        if previous_response.get("status") == 200:
            self.update_service_response({"status": 500, "message": error})


@csrf_exempt
def generate_otp_api(request):
    request_handler = RequestResponseHandler()
    if request.method != 'POST':
        request_handler.update_error_response('POST required')
        return JsonResponse(json.loads(request_handler.get_service_response()), status=405)
    try:
        email = '<EMAIL>'
        if not email:
            raise Exception('Email required')
        otp, otp_hash = generate_otp(email)
        msg = EmailMessage()
        msg["Subject"] = f"SCR script execution OTP code"
        msg["From"] = sender_email
        msg["To"] = receiver_email
        content = f'Your OTP is: {otp}'
        msg.set_content(content)

        try:
            with smtplib.SMTP_SSL("smtp.gmail.com", 465) as smtp:
                smtp.login(sender_email, email_password)
                smtp.send_message(msg)
        except Exception as e:
            import traceback
            traceback.print_exc()
            raise Exception('Failed to send mail')

        request_handler.update_service_response({'status': 200, 'message': 'Mail sent successfully', 'otp_hash': otp_hash})
    except Exception as e:
        request_handler.update_error_response(str(e))
    return JsonResponse(json.loads(request_handler.get_service_response()))


@csrf_exempt
def validate_otp_api(request):
    request_handler = RequestResponseHandler()
    if request.method != 'POST':
        request_handler.update_error_response('POST required')
        return JsonResponse(json.loads(request_handler.get_service_response()), status=405)
    try:
        data = json.loads(request.body)
        email = '<EMAIL>'
        otp = '{:06d}'.format(data.get('otp'))
        otp_hash = data.get('otp_hash')

        if not email or not otp or not otp_hash:
            raise Exception('You are not authorised to perform this action')

        print("hash_otp(otp) >>>>.", hash_otp(otp))
        run_script_task.delay()
        if validate_otp(email, otp_hash) and hash_otp(otp) == otp_hash:
            request_handler.update_service_response({'status': 200, 'message': 'OTP valid, script scheduled'})
        else:
            raise Exception('Invalid or expired OTP')
    except Exception as e:
        request_handler.update_error_response(str(e))
    return JsonResponse(json.loads(request_handler.get_service_response()))
