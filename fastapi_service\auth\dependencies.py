"""
Authentication dependencies for FastAPI
Converted from Django decorators
"""

from fastapi import HTTPEx<PERSON>, <PERSON><PERSON>, Depends, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Optional
import json

security = HTTPBearer()

async def token_validator(token: Optional[str] = Header(None)):
    """
    Validate token from header
    Converted from Django token_validator decorator
    """
    if not token:
        raise HTTPException(
            status_code=401,
            detail={"status": 1000, "message": "Token not found"}
        )
    return token

async def validate_request_method(request: Request, allowed_methods: list = ["POST"]):
    """
    Validate HTTP method
    Converted from Django method_validate decorator
    """
    if request.method not in allowed_methods:
        raise HTTPException(
            status_code=405,
            detail={"status": 405, "message": "The request method is not allowed"}
        )
    return True

async def validate_request_body(request: Request):
    """
    Validate request body exists and is valid JSO<PERSON>
    Converted from Django request_parameter_validate decorator
    """
    try:
        body = await request.body()
        if not body:
            raise HTTPException(
                status_code=400,
                detail={"status": 400, "message": "Invalid request body"}
            )
        
        request_params = json.loads(body)
        if not request_params:
            raise HTTPException(
                status_code=400,
                detail={"status": 400, "message": "Invalid request body"}
            )
        return request_params
    except json.JSONDecodeError:
        raise HTTPException(
            status_code=400,
            detail={"status": 400, "message": "Invalid JSON in request body"}
        )

def validate_required_keys(required_keys: list):
    """
    Factory function to create a dependency that validates required keys
    Converted from Django validate_keys decorator
    """
    async def _validate_keys(request_params: dict = Depends(validate_request_body)):
        missing_keys = [key for key in required_keys if key not in request_params]
        if missing_keys:
            raise HTTPException(
                status_code=400,
                detail={"status": 1000, "message": f"Missing keys {', '.join(missing_keys)}"}
            )
        return request_params
    return _validate_keys
