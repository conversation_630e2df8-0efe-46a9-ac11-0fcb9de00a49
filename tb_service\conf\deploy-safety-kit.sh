sudo mkdir /opt/deployment/safety-kit-detection
cd /opt/deployment/safety-kit-detection
sudo git clone https://github.com/schnellenergy/Luminator-Safety-Kit-Detection.git .

sudo apt install docker
sudo nano .env
# Place the env file contents

sudo docker build -t safety_kit_app .
sudo docker run -d -p 9211:9211 --name fastapi-container safety_kit_app

# To Check Logs
sudo docker logs fastapi-container
# To list all containers including stopped ones
sudo docker ps -a

# Check whether the app is running using wget
wget http://localhost:9211
