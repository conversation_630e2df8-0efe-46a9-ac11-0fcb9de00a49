sudo mkdir /opt/deployment/schnelliot/safety-kit-detection
cd /opt/deployment/schnelliot/safety-kit-detection
sudo git clone https://github.com/schnellenergy/schnelliot-safety-kit-detection.git .

sudo apt install docker
sudo nano .env
# Place the env file contents

sudo docker build -t safety_kit_app .
sudo docker run -d -p 9211:9211 --name fastapi-container safety_kit_app
# SL-02-196 : 6a2a1bcfff28724b2c696c5e3d90ac285f504e5a5c4e37947663759bef6dac4d
# To Check Logs
sudo docker logs fastapi-container
# To list all containers including stopped ones
sudo docker ps -a

docker compose down

sudo docker stop fastapi-container
sudo docker rm fastapi-container

# Check whether the app is running using wget
wget http://localhost:9211
