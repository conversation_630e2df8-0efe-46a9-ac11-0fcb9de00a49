import urllib3

from lume.service import *

logger = logging.getLogger(__name__)


@method_validate("GET")
def smart_lights_sms(request):
    try:
        _url = "https://api.instaalerts.zone/SendSMS/sendmsg.php"
        username = request.GET.get("uname")
        password = request.GET.get("pass")
        phoneno = request.GET.get("dest")
        message = urllib3.request.urlencode({"msg": request.GET.get("msg")})
        request_url = 'https://api.instaalerts.zone/SendSMS/sendmsg.php?uname=' + username + '&pass=' + password + '&send=SCHNEL&dest=' + phoneno + '&' + message
        logger.debug("[SMS-REQ] On behalf of SmartLights: %s" % request_url)
        response = requests.post(url=request_url)
        logger.debug("[SMS-RES] On behalf of SmartLights: %s" % response)
        return HttpResponse(response)
    except Exception as e:
        logger.exception(e)
        return HttpResponse(str(e))


@method_validate("POST")
def ghmc_transact(request):
    try:
        headers = {'Content-type': 'application/json', 'Accept': 'text/plain'}
        tr_data = request.body.decode('utf-8')
        transact_url = "https://ghmcmobileapp.ghmc.gov.in/StreetLights/Insert_Streetlight_Data_ArrayList"
        logger.debug("[GHMC] On behalf of SmartLights: %s" % transact_url)
        response = requests.post(url=transact_url, headers=headers, data=tr_data, timeout=1200)
        logger.debug("[GHMC] On behalf of SmartLights: %s" % response)
        return HttpResponse(response)
    except Exception as e:
        logger.exception(e)
        return HttpResponse(str(e))


@method_validate("POST")
def ap_transact(request):
    try:
        headers = {'Content-type': 'application/json', 'Accept': 'text/plain'}
        tr_data = request.POST.get()
        transact_url = "http://webservice.apccms.in/pan/panchayatservice.php"
        logger.debug("[AP] On behalf of SmartLights: %s" % transact_url)
        response = requests.post(url=transact_url, headers=headers, data=tr_data, timeout=1200)
        logger.debug("[AP] On behalf of SmartLights: %s" % response)
        return HttpResponse(response)
    except Exception as e:
        logger.exception(e)
        return HttpResponse(str(e))


def cochin_login():
    try:
        logger.debug("CSML data forwarding - Login...")
        header = {'Content-Type': 'application/x-www-form-urlencoded'}
        response = requests.post(CSML_LOGIN_URL, headers=header, data=kochin_user_data, timeout=140, verify=False)
        result = json.loads(response.text)
        return result['access_token']
    except Exception as e:
        logger.exception("cochin customer login %s" % str(e))
    return None


@method_validate("POST")
def lamp_failure_details(request):
    request_params = json.loads(request.body)
    try:
        header = {'Content-Type': 'application/json'}
        auth_token = cochin_login()
        header['Authorization'] = 'bearer ' + " " + auth_token
        logger.debug("CSML data forwarding - Lamp Failure Report: %s, %s" % (CSML_LAMP_FAILURE_URL, request_params))
        response = requests.post(url=CSML_LAMP_FAILURE_URL, headers=header, data=json.dumps(request_params),
                                 timeout=140, verify=False)
        logger.debug("CSML data forwarding - success: %s", json.loads(response.text))
        response.raise_for_status()
        return HttpResponse("ok")
    except Exception as e:
        logger.exception("CSML data forwarding - failure: %s" % e)
