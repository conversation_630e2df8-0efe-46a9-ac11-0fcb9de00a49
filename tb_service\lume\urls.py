from django.urls import path

from lume import patch_views
from lume import views

urlpatterns = [
    # TODO On behalf of SL1
    path('ghmc/tran_ghmc/', patch_views.ghmc_transact),
    path('ghmc/tran_ap/', patch_views.ghmc_transact),
    path('cochin/lamp/failure/', patch_views.lamp_failure_details),

    # SL2 - Luminator APIs
    path('login/', views.login),
    path('iam_user/', views.add_iam_user),
    path('print_qr/', views.get_current_user_role),
    path('get/device/', views.get_device_details),
    path('device/service/', views.device_take_service),

    path('entities/search/', views.entities_search),
    path('entity/detail/', views.entity_detail),
    path('entity/search/', views.entity_detail), # will be deprecated
    path('entities/', views.get_entities),

    path('get/assets/', views.get_asset_lat_lon),
    path('customers/', views.get_customers),
    path('regions/', views.get_regions),
    path('zones/', views.get_zones),
    path('wards/', views.get_wards),

    path('gw/dispatch/', views.dispatch_gw),
    path('gw/install/', views.gw_install),
    path('gw/update/', views.update_gw_attribute_details),
    path('gw/control/', views.gw_control),
    path('gw/replace/', views.gw_replace),

    path('ebmeter/replace/', views.ebmeter_replace),
    path('ebmeter/isinstallable/', views.ebmeter_isinstallable),

    path('ilm/test/', views.ilm_test_initiate_and_reset),
    path('ilm/update/', views.update_ilm_attribute_details),
    path('ilm/control/', views.ilm_control),

    path('pole/install/', views.pole_install),
    path('get/pole/', views.get_pole),
    path('get/entity/count/', views.get_pole_count),
    path('get/user/installation/count/', views.get_asset_installation_history),

    path('luminaire/maintain/', views.luminaire_maintain),

    path('get/project/wattage/', views.get_project_wise_wattage),

    path('image/upload/', views.image_upload_to_aws),
    path('logout/', views.logout),

    path('tickets/serializer/', views.ticket_name_serializer, name='ticket_detail'),
    path('lightpoint/commission', views.commission_lightpoint),
    path('lightpoint/location', views.update_latitude_longitude),

    path('login/ppe', views.upload_ppe_image),
    path('attendance/swipes', views.greyt_hr_swipes),

    path('switch_point/install/', views.switch_point_install),
    path('transformer/install/', views.transformer_install),
    # path('get/switch_point/', views.get_switch_point),
    # path('get/switch_point/count/', views.get_pole_count),
]
