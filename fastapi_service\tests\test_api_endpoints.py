"""
Comprehensive test suite for all API endpoints
Tests the FastAPI conversion using requests package
"""

import requests
import json
import logging
import time
from typing import Dict, Any, Optional

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class APITester:
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.auth_token = None
        self.test_results = []
        
        # Test credentials
        self.username = "<EMAIL>"
        self.password = "sethu@123"
    
    def log_test_result(self, endpoint: str, method: str, status_code: int, 
                       response_data: Any, success: bool, error: str = None):
        """Log test result"""
        result = {
            "endpoint": endpoint,
            "method": method,
            "status_code": status_code,
            "response_data": response_data,
            "success": success,
            "error": error,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        self.test_results.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        logger.info(f"{status} {method} {endpoint} - Status: {status_code}")
        if error:
            logger.error(f"Error: {error}")
    
    def test_login(self) -> bool:
        """Test login endpoint and get authentication token"""
        endpoint = "/api/login/"
        url = self.base_url + endpoint
        
        try:
            data = {
                "username": self.username,
                "password": self.password,
                "ts": str(int(time.time() * 1000)),
                "latitude": "12.9716",
                "longitude": "77.5946"
            }
            
            response = self.session.post(url, data=data)
            response_data = response.json()
            
            success = response.status_code == 200 and response_data.get("status") == 200
            if success and "token" in response_data:
                self.auth_token = response_data["token"]
                self.session.headers.update({"token": self.auth_token})
            
            self.log_test_result(endpoint, "POST", response.status_code, response_data, success)
            return success
            
        except Exception as e:
            self.log_test_result(endpoint, "POST", 0, None, False, str(e))
            return False
    
    def test_get_current_user_role(self) -> bool:
        """Test get current user role endpoint"""
        endpoint = "/api/print_qr/"
        url = self.base_url + endpoint
        
        try:
            response = self.session.post(url)
            response_data = response.json()
            
            success = response.status_code == 200
            self.log_test_result(endpoint, "POST", response.status_code, response_data, success)
            return success
            
        except Exception as e:
            self.log_test_result(endpoint, "POST", 0, None, False, str(e))
            return False
    
    def test_get_device_details(self) -> bool:
        """Test get device details endpoint"""
        endpoint = "/api/get/device/"
        url = self.base_url + endpoint
        
        try:
            data = {"device": "TEST_DEVICE_001"}
            
            response = self.session.post(url, json=data)
            response_data = response.json()
            
            success = response.status_code == 200
            self.log_test_result(endpoint, "POST", response.status_code, response_data, success)
            return success
            
        except Exception as e:
            self.log_test_result(endpoint, "POST", 0, None, False, str(e))
            return False
    
    def test_entities_search(self) -> bool:
        """Test entities search endpoint"""
        endpoint = "/api/entities/search/"
        url = self.base_url + endpoint
        
        try:
            params = {"name": "TEST"}
            
            response = self.session.get(url, params=params)
            response_data = response.json()
            
            success = response.status_code == 200
            self.log_test_result(endpoint, "GET", response.status_code, response_data, success)
            return success
            
        except Exception as e:
            self.log_test_result(endpoint, "GET", 0, None, False, str(e))
            return False
    
    def test_entity_detail(self) -> bool:
        """Test entity detail endpoint"""
        endpoint = "/api/entity/detail/"
        url = self.base_url + endpoint
        
        try:
            params = {"entityName": "TEST_ENTITY"}
            
            response = self.session.get(url, params=params)
            response_data = response.json()
            
            success = response.status_code == 200
            self.log_test_result(endpoint, "GET", response.status_code, response_data, success)
            return success
            
        except Exception as e:
            self.log_test_result(endpoint, "GET", 0, None, False, str(e))
            return False
    
    def test_device_service(self) -> bool:
        """Test device service endpoint"""
        endpoint = "/api/device/service/"
        url = self.base_url + endpoint
        
        try:
            data = {
                "deviceId": "test-device-id",
                "condition": "SERVICE"
            }
            
            response = self.session.post(url, json=data)
            response_data = response.json()
            
            success = response.status_code == 200
            self.log_test_result(endpoint, "POST", response.status_code, response_data, success)
            return success
            
        except Exception as e:
            self.log_test_result(endpoint, "POST", 0, None, False, str(e))
            return False
    
    def test_ilm_device_onboard(self) -> bool:
        """Test ILM device onboarding endpoint"""
        endpoint = "/onboard/ilm/1234567890123456/SLNNSGINSTDGX01A/"
        url = self.base_url + endpoint
        
        try:
            response = self.session.get(url)
            response_data = response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text
            
            success = response.status_code == 200
            self.log_test_result(endpoint, "GET", response.status_code, response_data, success)
            return success
            
        except Exception as e:
            self.log_test_result(endpoint, "GET", 0, None, False, str(e))
            return False
    
    def test_gateway_device_onboard(self) -> bool:
        """Test Gateway device onboarding endpoint"""
        endpoint = "/onboard/gw/123456789012345/GW_VARIANT/"
        url = self.base_url + endpoint
        
        try:
            response = self.session.get(url)
            response_data = response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text
            
            success = response.status_code == 200
            self.log_test_result(endpoint, "GET", response.status_code, response_data, success)
            return success
            
        except Exception as e:
            self.log_test_result(endpoint, "GET", 0, None, False, str(e))
            return False
    
    def test_logout(self) -> bool:
        """Test logout endpoint"""
        endpoint = "/api/logout/"
        url = self.base_url + endpoint
        
        try:
            data = {
                "ts": str(int(time.time() * 1000)),
                "latitude": "12.9716",
                "longitude": "77.5946"
            }
            
            response = self.session.post(url, data=data)
            response_data = response.json()
            
            success = response.status_code == 200
            self.log_test_result(endpoint, "POST", response.status_code, response_data, success)
            return success
            
        except Exception as e:
            self.log_test_result(endpoint, "POST", 0, None, False, str(e))
            return False

    def run_all_tests(self) -> Dict[str, Any]:
        """Run all API tests"""
        logger.info("🚀 Starting comprehensive API testing...")
        logger.info(f"Testing against: {self.base_url}")
        logger.info(f"Using credentials: {self.username}")

        # Test sequence
        tests = [
            ("Login", self.test_login),
            ("Get Current User Role", self.test_get_current_user_role),
            ("Get Device Details", self.test_get_device_details),
            ("Entities Search", self.test_entities_search),
            ("Entity Detail", self.test_entity_detail),
            ("Device Service", self.test_device_service),
            ("ILM Device Onboard", self.test_ilm_device_onboard),
            ("Gateway Device Onboard", self.test_gateway_device_onboard),
            ("Logout", self.test_logout),
        ]

        passed = 0
        failed = 0

        for test_name, test_func in tests:
            logger.info(f"\n📋 Running test: {test_name}")
            try:
                if test_func():
                    passed += 1
                else:
                    failed += 1
            except Exception as e:
                logger.error(f"Test {test_name} crashed: {str(e)}")
                failed += 1

        # Generate summary
        total = passed + failed
        success_rate = (passed / total * 100) if total > 0 else 0

        summary = {
            "total_tests": total,
            "passed": passed,
            "failed": failed,
            "success_rate": f"{success_rate:.1f}%",
            "test_results": self.test_results
        }

        logger.info(f"\n📊 TEST SUMMARY")
        logger.info(f"Total Tests: {total}")
        logger.info(f"Passed: {passed}")
        logger.info(f"Failed: {failed}")
        logger.info(f"Success Rate: {success_rate:.1f}%")

        return summary

    def generate_test_report(self, filename: str = "test_results.json"):
        """Generate detailed test report"""
        summary = self.run_all_tests()

        with open(filename, 'w') as f:
            json.dump(summary, f, indent=2)

        logger.info(f"📄 Test report saved to: {filename}")
        return summary

def main():
    """Main test runner"""
    # You can change the base_url to match your FastAPI server
    tester = APITester(base_url="http://localhost:8000")

    # Run tests and generate report
    results = tester.generate_test_report("api_test_results.json")

    # Print detailed results
    print("\n" + "="*80)
    print("🔍 DETAILED TEST RESULTS")
    print("="*80)

    for result in results["test_results"]:
        status = "✅ PASS" if result["success"] else "❌ FAIL"
        print(f"\n{status} {result['method']} {result['endpoint']}")
        print(f"   Status Code: {result['status_code']}")
        print(f"   Timestamp: {result['timestamp']}")
        if result["error"]:
            print(f"   Error: {result['error']}")
        if result["response_data"]:
            print(f"   Response: {json.dumps(result['response_data'], indent=2)[:200]}...")

    return results

if __name__ == "__main__":
    main()
