#!/usr/bin/env python3
"""
Test runner script for Schnelliot FastAPI
This script runs the comprehensive test suite and generates a detailed report
"""

import subprocess
import sys
import time
import requests
import json
from tests.test_api_endpoints import main as run_tests

def check_server_health(base_url: str = "http://localhost:8000", max_retries: int = 30):
    """Check if the FastAPI server is running and healthy"""
    print(f"🔍 Checking server health at {base_url}")
    
    for i in range(max_retries):
        try:
            response = requests.get(f"{base_url}/health", timeout=5)
            if response.status_code == 200:
                print(f"✅ Server is healthy!")
                return True
        except requests.exceptions.RequestException:
            pass
        
        print(f"⏳ Waiting for server... ({i+1}/{max_retries})")
        time.sleep(2)
    
    print(f"❌ Server is not responding after {max_retries} attempts")
    return False

def start_server():
    """Start the FastAPI server"""
    print("🚀 Starting FastAPI server...")
    try:
        # Start server in background
        process = subprocess.Popen([
            sys.executable, "-m", "uvicorn", "main:app", 
            "--host", "0.0.0.0", "--port", "8000", "--reload"
        ])
        return process
    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        return None

def main():
    """Main function to run tests"""
    print("="*80)
    print("🧪 SCHNELLIOT FASTAPI TEST SUITE")
    print("="*80)
    
    # Check if server is already running
    if not check_server_health():
        print("\n🔧 Server not running, attempting to start...")
        server_process = start_server()
        
        if server_process:
            # Wait for server to start
            time.sleep(5)
            if not check_server_health():
                print("❌ Failed to start server")
                if server_process:
                    server_process.terminate()
                return False
        else:
            print("❌ Could not start server. Please start it manually:")
            print("   uvicorn main:app --host 0.0.0.0 --port 8000 --reload")
            return False
    
    # Run the tests
    print("\n🧪 Running comprehensive API tests...")
    try:
        results = run_tests()
        
        # Print summary
        print("\n" + "="*80)
        print("📊 FINAL TEST SUMMARY")
        print("="*80)
        print(f"Total Tests: {results['total_tests']}")
        print(f"Passed: {results['passed']}")
        print(f"Failed: {results['failed']}")
        print(f"Success Rate: {results['success_rate']}")
        
        if results['failed'] == 0:
            print("\n🎉 ALL TESTS PASSED! The FastAPI conversion is working correctly.")
        else:
            print(f"\n⚠️  {results['failed']} tests failed. Please check the detailed results.")
        
        print(f"\n📄 Detailed test report saved to: api_test_results.json")
        
        return results['failed'] == 0
        
    except Exception as e:
        print(f"❌ Test execution failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
