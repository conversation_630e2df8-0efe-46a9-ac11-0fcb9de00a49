import unittest

from lume.views import *

Base_url = "http://192.168.21.46:8051/api/"

testing_Base_url = "http://127.0.0.1:8000/api/login/"


# Create your tests here.
class RemoveLamp(unittest.TestCase):
    sample_data1 = {
        "wards": [
            {
                "name": "ERO2-2",
                "id": "2f97f370-fb7b-11ed-8e35-33a78cfa9a56",
                "type": "ward"
            },
            {
                "name": "ERO1-10",
                "id": "229b5db0-fb7b-11ed-8e35-33a78cfa9a56",
                "type": "ward"
            },
            {
                "name": "ERO2-4",
                "id": "2f97a550-fb7b-11ed-8e35-33a78cfa9a56",
                "type": "ward"
            },
            {
                "name": "ERO1-8",
                "id": "2299fe20-fb7b-11ed-8e35-33a78cfa9a56",
                "type": "ward"
            },
            {
                "name": "ERO1-2",
                "id": "226580a0-fb7b-11ed-8e35-33a78cfa9a56",
                "type": "ward"
            },
            {
                "name": "ERO1-5",
                "id": "2263abe0-fb7b-11ed-8e35-33a78cfa9a56",
                "type": "ward"
            },
            {
                "name": "ERO1-7",
                "id": "22920ee0-fb7b-11ed-8e35-33a78cfa9a56",
                "type": "ward"
            },
            {
                "name": "ERO1-9",
                "id": "229bd2e0-fb7b-11ed-8e35-33a78cfa9a56",
                "type": "ward"
            },
            {
                "name": "ERO2-1",
                "id": "2f977e40-fb7b-11ed-8e35-33a78cfa9a56",
                "type": "ward"
            },
            {
                "name": "ERO1-4",
                "id": "2261b010-fb7b-11ed-8e35-33a78cfa9a56",
                "type": "ward"
            },
            {
                "name": "ERO1-6",
                "id": "22635dc0-fb7b-11ed-8e35-33a78cfa9a56",
                "type": "ward"
            },
            {
                "name": "ERO2-3",
                "id": "2faa6a00-fb7b-11ed-8e35-33a78cfa9a56",
                "type": "ward"
            },
            {
                "name": "ERO1-3",
                "id": "2264bd50-fb7b-11ed-8e35-33a78cfa9a56",
                "type": "ward"
            },
            {
                "name": "ERO2-5",
                "id": "2f9f4670-fb7b-11ed-8e35-33a78cfa9a56",
                "type": "ward"
            },
            {
                "name": "ERO1-1",
                "id": "22581320-fb7b-11ed-8e35-33a78cfa9a56",
                "type": "ward"
            }
        ]
    }
    sample_data2 = {
        "wards": [
            {
                "name": "ERO1-10",
                "id": "229b5db0-fb7b-11ed-8e35-33a78cfa9a56",
                "type": "ward"
            },
            {
                "name": "ERO1-4",
                "id": "2261b010-fb7b-11ed-8e35-33a78cfa9a56",
                "type": "ward"
            },
            {
                "name": "ERO1-8",
                "id": "2299fe20-fb7b-11ed-8e35-33a78cfa9a56",
                "type": "ward"
            },
            {
                "name": "ERO1-6",
                "id": "22635dc0-fb7b-11ed-8e35-33a78cfa9a56",
                "type": "ward"
            },
            {
                "name": "ERO1-3",
                "id": "2264bd50-fb7b-11ed-8e35-33a78cfa9a56",
                "type": "ward"
            },
            {
                "name": "ERO1-2",
                "id": "226580a0-fb7b-11ed-8e35-33a78cfa9a56",
                "type": "ward"
            },
            {
                "name": "ERO1-5",
                "id": "2263abe0-fb7b-11ed-8e35-33a78cfa9a56",
                "type": "ward"
            },
            {
                "name": "ERO1-7",
                "id": "22920ee0-fb7b-11ed-8e35-33a78cfa9a56",
                "type": "ward"
            },
            {
                "name": "ERO1-9",
                "id": "229bd2e0-fb7b-11ed-8e35-33a78cfa9a56",
                "type": "ward"
            },
            {
                "name": "ERO1-1",
                "id": "22581320-fb7b-11ed-8e35-33a78cfa9a56",
                "type": "ward"
            }
        ]
    }
    sample_data3 = {
        "wards": [
            {
                "name": "ERO2-1",
                "id": "2f977e40-fb7b-11ed-8e35-33a78cfa9a56",
                "type": "ward"
            },
            {
                "name": "ERO2-2",
                "id": "2f97f370-fb7b-11ed-8e35-33a78cfa9a56",
                "type": "ward"
            },
            {
                "name": "ERO2-4",
                "id": "2f97a550-fb7b-11ed-8e35-33a78cfa9a56",
                "type": "ward"
            },
            {
                "name": "ERO2-3",
                "id": "2faa6a00-fb7b-11ed-8e35-33a78cfa9a56",
                "type": "ward"
            },
            {
                "name": "ERO2-5",
                "id": "2f9f4670-fb7b-11ed-8e35-33a78cfa9a56",
                "type": "ward"
            }
        ]
    }
    sample_data4 = {"wards": []}
    sample_data5 = {"status": 400, "message": "Invalid request body"}
    sample_data6 = {"status": 405, "message": "The request method is not allowed"}
    sample_data7 = {}

    def test_data1(self):
        api_url = Base_url + "wards/?zoneId=d8c8e5e0-fb7a-11ed-8e35-33a78cfa9a56"
        headers = {
            "token": "eyJhbGciOiJIUzUxMiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Tkeu9Vl-2INJLvYiBV0lEnlJaB9wQWMdx0jL_PesTrfZkiCpvKjgPPQkHKbr09QA0dk0B_mpNiGlPS9uF2eNpg"}
        response = requests.get(url=api_url, headers=headers, data="")
        self.assertEqual(self.sample_data1, json.loads(response.text), "message: passed successfully")

    def test_data2(self):
        api_url = Base_url + "wards/?zoneId=f96dadd0-fb7a-11ed-8e35-33a78cfa9a56"
        headers = {
            "token": "eyJhbGciOiJIUzUxMiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Tkeu9Vl-2INJLvYiBV0lEnlJaB9wQWMdx0jL_PesTrfZkiCpvKjgPPQkHKbr09QA0dk0B_mpNiGlPS9uF2eNpg"}
        response = requests.get(url=api_url, headers=headers, data="")
        self.assertEqual(self.sample_data2, json.loads(response.text), "message: passed successfully")

    def test_data3(self):
        api_url = Base_url + "wards/?zoneId=ff1c9ac0-fb7a-11ed-8e35-33a78cfa9a56"
        headers = {
            "token": "eyJhbGciOiJIUzUxMiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Tkeu9Vl-2INJLvYiBV0lEnlJaB9wQWMdx0jL_PesTrfZkiCpvKjgPPQkHKbr09QA0dk0B_mpNiGlPS9uF2eNpg"}
        response = requests.get(url=api_url, headers=headers, data="")
        self.assertEqual(self.sample_data3, json.loads(response.text), "message: passed successfully")

    def test_data4(self):
        api_url = Base_url + "wards/?zoneId=ff1c9ac0-fb7a-11ed-8e35-33a78c"
        headers = {
            "token": "eyJhbGciOiJIUzUxMiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Tkeu9Vl-2INJLvYiBV0lEnlJaB9wQWMdx0jL_PesTrfZkiCpvKjgPPQkHKbr09QA0dk0B_mpNiGlPS9uF2eNpg"}
        response = requests.get(url=api_url, headers=headers, data="")
        self.assertEqual(self.sample_data4, json.loads(response.text), "message: passed successfully")

    def test_data5(self):
        api_url = Base_url + "wards/?zoneId="
        headers = {
            "token": "eyJhbGciOiJIUzUxMiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Tkeu9Vl-2INJLvYiBV0lEnlJaB9wQWMdx0jL_PesTrfZkiCpvKjgPPQkHKbr09QA0dk0B_mpNiGlPS9uF2eNpg"}
        response = requests.get(url=api_url, headers=headers, data="")
        self.assertEqual(self.sample_data5, json.loads(response.text), "message: passed successfully")

    def test_data6(self):
        api_url = Base_url + "wards/?zoneId="
        headers = {
            "token": "eyJhbGciOiJIUzUxMiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Tkeu9Vl-2INJLvYiBV0lEnlJaB9wQWMdx0jL_PesTrfZkiCpvKjgPPQkHKbr09QA0dk0B_mpNiGlPS9uF2eNpg"}
        response = requests.post(url=api_url, headers=headers, data="")
        self.assertEqual(self.sample_data6, json.loads(response.text), "message: Invalid request body")


class LoginTest(unittest.TestCase):
    sample_data1 = {
        "username": "<EMAIL>",
        "password": "schnell@123",
        "app": "luminator",
        "version": "3.7.4"
    }
    sample_data2 = {
        "username": "<EMAIL>",
        "password": "schnell@123",
        "app": "luminator",
        "version": "3.6.9"
    }
    sample_data3 = {
        "username": "<EMAIL>",
        "password": "schnell@123",
        "app": "luminator",
        "version": "3.8.6"
    }
    sample_data4 = {
        "username": "<EMAIL>",
        "password": "schnell@123",
        "app": "lume",
        "version": "2.1.4"
    }
    sample_data5 = {
        "username": "<EMAIL>",
        "password": "schnell@123",
        "app": "lume",
        "version": "2.0.7"
    }
    sample_data6 = {
        "username": "<EMAIL>",
        "password": "schnell@123",
        "app": "lume",
        "version": "2.1.7"
    }
    sample_data7 = {
        "username": "<EMAIL>",
        "password": "schnell@123",
        "app": "signify",
        "version": "3.7.3"
    }
    sample_data8 = {
        "username": "<EMAIL>",
        "password": "schnell@123",
        "app": "signify",
        "version": "3.6.3"
    }
    sample_data9 = {
        "username": "<EMAIL>",
        "password": "schnell@123",
        "app": "signify",
        "version": "3.8.6"
    }
    sample_data10 = {
        "username": "<EMAIL>",
        "password": "schnell@123",
        "app": "luminatr"
    }
    sample_data11 = {
        "username": "<EMAIL>",
        "password": "schnell@123",
        "app": "lume"
    }
    sample_data12 = {
        "username": "<EMAIL>",
        "password": "schnell@123",
        "app": "signify"
    }
    headers = {"Content-Type": "application/x-www-form-urlencoded"}

    def test_data1(self):
        api_url = testing_Base_url
        response = requests.post(url=api_url, json=self.sample_data1)
        self.assertTrue(json.loads(response.text), "message: logged in successfully")

    def test_data2(self):
        api_url = testing_Base_url
        response = requests.post(url=api_url, data=self.sample_data2, headers=self.headers)
        self.assertEqual({"status": 1001,
                          "message": "Kindly upgrade to latest version 3.7.4 from PlayStore to continue using this app!"},
                         json.loads(response.text), "message: login unsuccessful")

    def test_data3(self):
        api_url = testing_Base_url
        response = requests.post(url=api_url, json=self.sample_data3)
        self.assertTrue(json.loads(response.text), "message: logged in successfully")

    def test_data4(self):
        api_url = testing_Base_url
        response = requests.post(url=api_url, json=self.sample_data4)
        self.assertTrue(json.loads(response.text), "message: logged in successfully")

    def test_data5(self):
        api_url = testing_Base_url
        response = requests.post(url=api_url, data=self.sample_data5, headers=self.headers)
        self.assertEqual({"status": 1001,
                          "message": "Kindly upgrade to latest version 2.1.4 from PlayStore to continue using this app!"},
                         json.loads(response.text), "message: login unsuccessful")

    def test_data6(self):
        api_url = testing_Base_url
        response = requests.post(url=api_url, json=self.sample_data6)
        self.assertTrue(json.loads(response.text), "message: logged in successfully")

    def test_data7(self):
        api_url = testing_Base_url
        response = requests.post(url=api_url, json=self.sample_data7)
        self.assertTrue(json.loads(response.text), "message: logged in successfully")

    def test_data8(self):
        api_url = testing_Base_url
        response = requests.post(url=api_url, data=self.sample_data8, headers=self.headers)
        self.assertEqual({"status": 1001,
                          "message": "Kindly upgrade to latest version 3.7.3 from PlayStore to continue using this app!"},
                         json.loads(response.text), "message: login unsuccessful")

    def test_data9(self):
        api_url = testing_Base_url
        response = requests.post(url=api_url, json=self.sample_data9)
        self.assertTrue(json.loads(response.text), "message: logged in successfully")

    def test_data10(self):
        api_url = testing_Base_url
        response = requests.post(url=api_url, data=self.sample_data10, headers=self.headers)
        self.assertEqual({"status": 1001, "message": "Kindly provide input app name and version"},
                         json.loads(response.text), "message: login unsuccessful")
