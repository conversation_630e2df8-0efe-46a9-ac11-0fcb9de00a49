"""
Service classes for lume module
Converted from Django service classes
"""

import json
import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Union

from config.settings import settings, LOGIN_CREDENTIALS, LUME_PRINT_QR_REQUIRED_ROLE
from utils.handler import RequestResponseHandler

logger = logging.getLogger(__name__)

BASE_URL = settings.BASE_URL

class TbAuthController(ABC):
    login_url: str = "/api/auth/login"
    logout_url: str = "/api/auth/logout"
    get_all_permissions_url = "/api/permissions/allowedPermissions"
    header: dict = {'content-type': 'application/json'}
    login_response = {"token": "", "refresh_token": ""}
    get_role_url = "/api/roles?pageSize=1000&page=0"
    get_user_details_url = "/api/auth/user"
    get_user_info_by_id_url = "/api/user/info/{user_id}"

    @abstractmethod
    def login(self, user_name: str, password: str) -> str:
        pass

    @abstractmethod
    def logout(self, header: str) -> None:
        pass

    @abstractmethod
    def get_user_permissions(self, header: str) -> bool:
        pass

class TbAuthService(TbAuthController):
    def __init__(self, instance: RequestResponseHandler):
        self.instance = instance

    def login(self, user_name: str, password: str) -> dict:
        data = json.dumps({"username": user_name, "password": password})
        return RequestResponseHandler.post_request(url=BASE_URL + self.login_url,
                                                   header=self.header, data=data, instance=self.instance)

    def logout(self, header: str) -> None:
        self.header['X-Authorization'] = "Bearer " + header
        RequestResponseHandler.post_request(url=BASE_URL + self.logout_url, header=self.header, data='',
                                            instance=self.instance)

    def get_user_permissions(self, header: str) -> dict:
        url = BASE_URL + self.get_role_url
        self.header['X-Authorization'] = "Bearer " + header
        return RequestResponseHandler.get_request(url=url, header=self.header, data='', instance=self.instance)

    def get_user_details(self, header: str) -> dict:
        url = BASE_URL + self.get_user_details_url
        self.header['X-Authorization'] = "Bearer " + header
        return RequestResponseHandler.get_request(url=url, header=self.header, data='', instance=self.instance)

    def save_token(self, token: str) -> None:
        self.header['X-Authorization'] = token

    @staticmethod
    def get_roles(data: dict) -> bool:
        role_availability = False
        for role in data:
            if role.get("name").lower() == LUME_PRINT_QR_REQUIRED_ROLE:
                role_availability = True
        return role_availability

class TbEntityController(ABC):
    entity_by_id_url = "/api/{entity_type}/{entity_id}"
    entity_by_name_url = "/api/tenant/{entity_type}s?{query_name}={entity_name}"
    get_entity_info_by_id_url = "/api/{entity_type}/info/{entity_id}"
    entity_attribute_url = "/api/plugins/telemetry/{entity_type}/{entity_id}/values/attributes/{scope}?keys={keys}"
    update_entity_attribute_url = "/api/plugins/telemetry/{entity_type}/{entity_id}/attributes/{scope}"
    delete_entity_attribute_url = "/api/plugins/telemetry/{entity_type}/{entity_id}/{scope}?keys={keys}"
    create_relation = "/api/relation"
    get_to_relation_info = "/api/relations/info?toId={to_id}&toType={to_type}"
    remove_relation = "/api/relation?fromId={from_id}&fromType={from_type}&" \
                      "relationType={relation_type}&relationTypeGroup={relation_type_group}&toId={to_id}&toType={to_type}"
    get_from_relation_info = "/api/relations/info?fromId={from_id}&fromType={from_type}"
    get_entity_relations = "/api/relations?fromId={from_id}&fromType={from_type}"
    get_entity_telemetry_url = "/api/plugins/telemetry/{entity_type}/{entity_id}/values/timeseries?keys={keys}"
    update_entity_telemetry_url = "/api/plugins/telemetry/{entity_type}/{entity_id}/timeseries/{scope}"
    device_control_url = "/api/plugins/rpc/oneway/{device_id}"

    def get_entity_by_name(self, entity_type: str, name: str) -> dict:
        pass

    def get_entity_by_id(self, entity_type: str, entity_id: str) -> dict:
        pass

class EntityService(TbEntityController):
    def __init__(self, token: str, instance: RequestResponseHandler):
        self.header = {'content-type': 'application/json', 'X-Authorization': 'Bearer ' + token}
        self.relation_json = {"from": {}, "to": {}, "type": "", "typeGroup": "COMMON", "additionalInfo": {}}
        self.instance = instance

    @abstractmethod
    def get_entity_by_name(self, entity_type: str, name: str) -> dict:
        pass

    @abstractmethod
    def get_entity_by_id(self, entity_type: str, entity_id: str) -> dict:
        pass

class DeviceService(EntityService):
    def __init__(self, token: str, instance: RequestResponseHandler):
        super().__init__(token, instance)
        # RPC commands for ILM devices
        self.ilm_rpc = {
            "on": {"method": "setValue", "params": {"pin": 7, "value": 1}},
            "off": {"method": "setValue", "params": {"pin": 7, "value": 0}},
            "flash": {"method": "setValue", "params": {"pin": 7, "value": 2}},
            "dim30": {"method": "setValue", "params": {"pin": 7, "value": 30}},
            "dim50": {"method": "setValue", "params": {"pin": 7, "value": 50}},
            "dim70": {"method": "setValue", "params": {"pin": 7, "value": 70}},
            "dim100": {"method": "setValue", "params": {"pin": 7, "value": 100}},
            "rtc": {"method": "setRTC", "params": {}},
            "test": {"method": "test", "params": {}}
        }
        # RPC commands for Gateway devices
        self.gw_rpc = {
            "on": {"method": "setValue", "params": {"pin": 7, "value": 1}},
            "off": {"method": "setValue", "params": {"pin": 7, "value": 0}},
            "reboot": {"method": "reboot", "params": {}}
        }

    def save_token(self, token: str) -> None:
        self.header['X-Authorization'] = 'Bearer ' + token

    def get_entity_by_name(self, entity_type: str, name: str) -> dict:
        url = BASE_URL + self.entity_by_name_url.format(entity_type=entity_type, query_name="deviceName", entity_name=name)
        return RequestResponseHandler.get_request(url=url, header=self.header, data='', instance=self.instance)

    def get_entity_by_id(self, entity_type: str, entity_id: str) -> dict:
        url = BASE_URL + self.entity_by_id_url.format(entity_type=entity_type, entity_id=entity_id)
        return RequestResponseHandler.get_request(url=url, header=self.header, data='', instance=self.instance)

    def device_control(self, device_id: str, data: str) -> dict:
        url = BASE_URL + self.device_control_url.format(device_id=device_id)
        return RequestResponseHandler.post_request(url=url, header=self.header, data=data, instance=self.instance)

    def get_entity_attribute(self, entity_type: str, entity_id: str, scope: str, keys: str) -> dict:
        url = BASE_URL + self.entity_attribute_url.format(entity_type=entity_type, entity_id=entity_id, scope=scope, keys=keys)
        return RequestResponseHandler.get_request(url=url, header=self.header, data='', instance=self.instance)

    def update_entity_attribute(self, entity_type: str, entity_id: str, scope: str, data: str) -> dict:
        url = BASE_URL + self.update_entity_attribute_url.format(entity_type=entity_type, entity_id=entity_id, scope=scope)
        return RequestResponseHandler.post_request(url=url, header=self.header, data=data, instance=self.instance)

    def get_entity_to_relation_info(self, to_id: str, to_type: str) -> dict:
        url = BASE_URL + self.get_to_relation_info.format(to_id=to_id, to_type=to_type)
        return RequestResponseHandler.get_request(url=url, header=self.header, data='', instance=self.instance)

    def construct_attributes(self, request_attributes: dict, invalid_keys: list) -> dict:
        """Construct attributes by filtering out invalid keys"""
        return {key: value for key, value in request_attributes.items() if key not in invalid_keys}

class AssetService(EntityService):
    def __init__(self, token: str, instance: RequestResponseHandler, tenent_token: str = ''):
        self.tenent_header = {'content-type': 'application/json', 'X-Authorization': 'Bearer ' + tenent_token}
        super().__init__(token, instance)

    def save_token(self, token: str) -> None:
        self.header['X-Authorization'] = 'Bearer ' + token

    def get_entity_by_name(self, entity_type: str, name: str) -> dict:
        url = BASE_URL + self.entity_by_name_url.format(entity_type=entity_type, query_name="assetName", entity_name=name)
        return RequestResponseHandler.get_request(url=url, header=self.header, data='', instance=self.instance)

    def get_entity_by_id(self, entity_type: str, entity_id: str) -> dict:
        url = BASE_URL + self.entity_by_id_url.format(entity_type=entity_type, entity_id=entity_id)
        return RequestResponseHandler.get_request(url=url, header=self.header, data='', instance=self.instance)

    def update_telemetry(self, entity_type: str, entity_id: str, data: dict) -> dict:
        url = BASE_URL + self.update_entity_telemetry_url.format(entity_type=entity_type, entity_id=entity_id, scope="DEVICE")
        return RequestResponseHandler.post_request(url=url, header=self.header, data=json.dumps(data), instance=self.instance)

    def get_entity_attribute(self, entity_type: str, entity_id: str, scope: str, keys: str) -> list or None:
        url = BASE_URL + self.entity_attribute_url.format(entity_type=entity_type,
                                                          entity_id=entity_id, scope=scope, keys=keys)
        return RequestResponseHandler.get_request(url=url, header=self.header, data='', instance=self.instance)

class CustomerService:
    def __init__(self, token: str, instance: RequestResponseHandler):
        self.header = {'content-type': 'application/json', 'X-Authorization': 'Bearer ' + token}
        self.instance = instance
        self.get_owner_by_id_url = "/api/{owner_type}/{owner_id}"

    def save_token(self, token: str) -> None:
        self.header['X-Authorization'] = 'Bearer ' + token

    def get_customer_by_id(self, owner_type: str, owner_id: str) -> dict:
        url = BASE_URL + self.get_owner_by_id_url.format(owner_type=owner_type.lower(), owner_id=owner_id)
        return RequestResponseHandler.get_request(url=url, header=self.header, data='', instance=self.instance)

class AttendanceService(TbEntityController):

    def handle_login(self, _username: str, _password: str, request_handler: RequestResponseHandler,
                    _ts: str, latitude: str, longitude: str):
        """Handle user login with attendance tracking"""
        try:
            from config.settings import ATTRIBUTE_SCOPES
            auth_service = TbAuthService(instance=request_handler)
            _token = auth_service.login(user_name=_username, password=_password)
            if _token.get("token"):
                user_info = auth_service.get_user_details(header=_token.get("token"))
                _id = user_info.get("id").get("id")
                asset_service = AssetService(token=_token.get("token"), instance=request_handler)
                _data = asset_service.get_entity_attribute(
                    entity_type="USER",
                    entity_id=_id,
                    scope=ATTRIBUTE_SCOPES[1],
                    keys="isAttendanceCaptureRequired,isLocationTrackingRequired,isPPERequired,isCheckedIn,employeeId,locationTrackingInterval")
                _result = {item["key"]: item["value"] for item in _data}

                # Add token to result
                _result["token"] = _token.get("token")
                _result["refreshToken"] = _token.get("refreshToken", "")

                # Handle attendance logic here (simplified for now)
                telemetry_data = {"latitude": latitude, "longitude": longitude}
                if telemetry_data:
                    asset_service.update_telemetry(
                        entity_type="USER",
                        entity_id=_id,
                        data=telemetry_data)
                request_handler.update_service_response(_result)
            else:
                logger.exception("Login failed due to no token found")
        except Exception as e:
            logger.exception("Login failed due to - %s" % str(e))

    def handle_logout(self, _token: str, request_handler: RequestResponseHandler,
                     _username: str, _ts: str, latitude: str, longitude: str):
        """Handle user logout with attendance tracking"""
        try:
            from config.settings import ATTRIBUTE_SCOPES
            auth_service = TbAuthService(instance=request_handler)
            asset_service = AssetService(token=_token, instance=request_handler)
            user_info = auth_service.get_user_details(header=_token)
            _id = user_info.get("id").get("id")

            # Update telemetry
            telemetry_data = {"latitude": latitude, "longitude": longitude}
            asset_service.update_telemetry(
                entity_type="USER",
                entity_id=_id,
                data=telemetry_data)
            auth_service.logout(header=_token)
            logger.info("User Log out Successful: %s" % _username)
            request_handler.update_service_response({"status": 200})
        except Exception as e:
            logger.exception("Logout failed due to - %s" % str(e))

class UtilService:
    """Utility service for various operations like AWS uploads"""
    def __init__(self):
        pass

    def upload_image_to_aws(self, request_handler: RequestResponseHandler,
                           img_data: str, file_name: str, folder: str) -> dict:
        """Upload image to AWS S3 (simplified implementation)"""
        try:
            # This would contain the actual AWS S3 upload logic
            # For now, return a mock response
            response = {
                "status": 200,
                "message": "Image uploaded successfully",
                "ppe_image_url": f"https://s3.amazonaws.com/{settings.AWS_BUCKET_NAME}/{folder}/{file_name}"
            }
            return response
        except Exception as e:
            logger.exception("Failed to upload image to AWS: %s" % str(e))
            return {"status": 500, "message": "Failed to upload image"}
