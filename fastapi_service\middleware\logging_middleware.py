"""
Logging middleware for FastAPI
Converted from Django LogRequestMiddleware
"""

import logging
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request
from fastapi import FastAPI

logger = logging.getLogger(__name__)

class LogRequestMiddleware(BaseHTTPMiddleware):
    """
    FastAPI middleware to log the URL and parameters for GET and POST requests.
    """
    
    async def dispatch(self, request: Request, call_next):
        method = request.method.upper()
        url = request.url.path
        
        # Skip logging for certain paths
        excluded_paths = [
            '/api/sms/send/', 
            '/api/ghmc/tran_ghmc/', 
            '/api/ghmc/tran_ap/', 
            '/api/cochin/lamp/failure/'
        ]
        
        if url not in excluded_paths:
            # Log request details
            logger.debug(f"{method} {url}")
            
        # Continue processing
        response = await call_next(request)
        return response
