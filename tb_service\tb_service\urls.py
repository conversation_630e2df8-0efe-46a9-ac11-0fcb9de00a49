"""tb_service URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/3.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
import os

from django.conf import settings
from django.conf.urls.static import static
from django.contrib import admin
from django.urls import include, path

site_media = os.path.join(os.path.dirname(__file__), 'media')

urlpatterns = [
                  path('admin/', admin.site.urls),
                  path('api/', include('lume.urls')),
                  path('onboard/', include('onboarder.urls')),
                  path('api/dashboard/', include('dashboard.urls'))
              ] + static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
