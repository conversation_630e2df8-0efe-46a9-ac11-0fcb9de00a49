import json
import requests
import os
import boto3

API_BASE_URL = os.environ['API_BASE_URL']
LOGIN_CREDENTIALS = os.environ['LOGIN_CREDENTIALS']
AWS_REGION = os.environ['REGION_NAME']
FAILED_LOG_TABLE = boto3.resource('dynamodb', region_name=AWS_REGION).Table("EntityDetailUpdateFailLog")


def lambda_handler(event, context):
    """
    Receive Construct data from SQS and Update Server Attributes
    """
    header = login(API_BASE_URL, LOGIN_CREDENTIALS)
    for record in event["Records"]:
        for process_data in json.loads(record['body'])['construct_data']:
            update_server_attributes(process_data, header)
    return True


def update_server_attributes(process_data, header):
    """
    Update the Server Attributes to the Entity
    :param process_data:
    :param header:
    """
    api_url = API_BASE_URL + "/plugins/telemetry/" + str(process_data["entity_type"]) + "/" + str(
        process_data["id"]) + "/attributes/SERVER_SCOPE"
    try:
        query_response = requests.post(api_url, headers=header, data=json.dumps(process_data["SERVER_ATTRIBUTES"]),
                                       timeout=90)
        print(query_response)
        if query_response.status_code == 401:
            return update_server_attributes(process_data, login(API_BASE_URL, LOGIN_CREDENTIALS))
        elif query_response.status_code in [500, 503, 504]:
            insert_failed_log(process_data)
        query_response.raise_for_status()
        # print(process_data['name'], query_response)
    except Exception as e:
        print("Failed to Update Server Attributes - %s" % str(e))


def login(base_url, credentials):
    """
    Login into the Thingsboard
    :param base_url:
    :param credentials:
    :return:
    """
    api_url = base_url + "/auth/login"
    auth_header = {'Content-type': 'application/json', 'Accept': '*/*'}
    try:
        login_response = requests.post(api_url, headers=auth_header, data=credentials, timeout=30)
        auth_token = json.loads(login_response.text).get('token')
        auth_header['X-Authorization'] = 'Bearer ' + auth_token
    except Exception as e:
        print('Login Failed  : ' + str(e))
    return auth_header


def insert_failed_log(item):
    try:
        item_data = {'entity_id': item['id'], 'entity_type': item['entity_type'],
                     'server_attr': json.dumps(item['SERVER_ATTRIBUTES'])}
        FAILED_LOG_TABLE.put_item(Item=item_data)
        return True
    except Exception as e:
        print("Couldn't insert fail log in DB:", e)
        return False
