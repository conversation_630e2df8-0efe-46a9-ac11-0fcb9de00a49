import json
import os
import sys
from pathlib import Path
import requests
import openpyxl

from dotenv import load_dotenv

# Configuring environment
BASE_DIR = Path(__file__).resolve().parent
load_dotenv(dotenv_path=os.path.join(BASE_DIR, '.env'))

API_KEY = os.getenv('API_KEY')


def fetch_street_details(latitude=None, longitude=None):
    if latitude is None and longitude is None:
        print("Latitude or Longitude column not found in the Excel sheet.")
        exit()

    url = f"https://maps.googleapis.com/maps/api/geocode/json?latlng={latitude},{longitude}&key={API_KEY}"
    result = []
    try:
        response = requests.get(url)
        if response.status_code == 200:
            data = response.json()
            if 'results' in data and len(data['results']) > 0:
                _address = data['results'][0]['formatted_address']
                result.append(_address)
                for results in data['results']:
                     if len(results['types']) == 1 and results['types'][0] == 'route':
                          _address = results['formatted_address'].replace("Unnamed Road,", "")

                result.append(_address)
            else:
                print("No results found.")
        else:
            print("Request failed with status code:", response.status_code)
    except Exception as e:
        print("An error occurred:", e)
    return result


def extract_lat_log_details(file_name, sheet_number=0):
    count = 1
    workbook = openpyxl.load_workbook(file_name)
    sheet = workbook.worksheets[sheet_number]

    latitude_column_index = None
    longitude_column_index = None
    address_column_index = None
    for cell in sheet[1]:
        if cell.value == "Latitude":
            latitude_column_index = cell.column
        elif cell.value == "Longitude":
            longitude_column_index = cell.column
        elif cell.value == "Address":
            address_column_index = cell.column

    if latitude_column_index is None or longitude_column_index is None:
        print("Latitude or Longitude column not found in the Excel sheet.")
        return

    if address_column_index is None:
        address_column_index = len(sheet[1]) + 1
        sheet.cell(row=1, column=address_column_index, value="Address")

    for idx, row in enumerate(sheet.iter_rows(min_row=2, values_only=True), start=2):
        print(f"Processing Row ${idx}")
        latitude = row[latitude_column_index - 1]
        longitude = row[longitude_column_index - 1]
        if row[address_column_index] is not None and len(row[address_column_index]) > 1:
            continue
        result = fetch_street_details(latitude, longitude)
        print(f"API hit done for Row ${idx}")
        if len(result) > 0:
            count += 1
            sheet.cell(row=idx, column=address_column_index, value=result[0])
            if len(result) > 1:
                sheet.cell(row=idx, column=address_column_index + 1, value=result[1])
            if count == 1000:
                break
        print(f"Fetched records $-{count}")
    workbook.save(file_name)


if __name__ == "__main__":
    if len(sys.argv) == 3:
        _file_name = sys.argv[1]
        _sheet_number = int(sys.argv[2])
        extract_lat_log_details(_file_name, sheet_number=_sheet_number)
        print("Execution success")
    else:
        print("Failure: Arguments missing")
        print("Sample:  python fetch_street_name.py sample_report.xlsx 1")
    # fetch_street_details(latitude=13.1165575, longitude=79.4117913)