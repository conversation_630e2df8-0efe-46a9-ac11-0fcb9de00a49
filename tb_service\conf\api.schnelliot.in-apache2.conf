Listen 9201
WSGISocketPrefix /var/run/wsgi
<VirtualHost *:9201>
    # Server Details
    ServerName api.schnelliot.in
    ServerAlias api.schnelliot.in # api.schnelliot.in
    ServerAdmin <EMAIL>

    # Log Details
    ErrorLog ${APACHE_LOG_DIR}/schnelliot-api-error.log
    CustomLog ${APACHE_LOG_DIR}/schnelliot-api-access.log combined

    # Project Details
    DocumentRoot /opt/deployment/schnelliot-api/tb_service

    # WSGI Configuration
    WSGIDaemonProcess api_schnelliot_in python-path=/opt/deployment/schnelliot-api/tb_service python-home=/opt/.envs/schnelliot-env
    WSGIProcessGroup api_schnelliot_in
    WSGIScriptAlias / /opt/deployment/schnelliot-api/tb_service/tb_service/wsgi.py

    <Directory /opt/deployment/schnelliot-api/tb_service/tb_service>
        <Files wsgi.py>
            Require all granted
        </Files>
    </Directory>

    # Static Directory
    Alias /static /opt/deployment/schnelliot-api/tb_service/static
    <Directory /opt/deployment/schnelliot-api/tb_service/static>
        Require all granted
    </Directory>

    # Media directory
    Alias /static /opt/deployment/schnelliot-api/tb_service/media
    <Directory /opt/deployment/schnelliot-api/tb_service/media>
        Require all granted
    </Directory>
</VirtualHost>

