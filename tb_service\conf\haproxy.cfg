https-in <name>
    ...
    acl siot_apib hdr(host) -i beta.api.schnelliot.in
    use_backend schnelliot-api if siot_apib

    acl siot_api hdr(host) -i api.schnelliot.in
    use_backend schnelliot-api if siot_api
    ...


backend schnelliot-api
    balance roundrobin
    option tcp-check
    option log-health-checks
    server siot_api 127.0.0.1:9201 check inter 5s
    http-request set-header X-Forwarded-Port %[dst_port]