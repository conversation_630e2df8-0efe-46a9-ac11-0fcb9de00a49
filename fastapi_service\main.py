"""
FastAPI main application entry point
Converted from Django tb_service project
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import logging
import sys
from pathlib import Path

from config.settings import settings
from middleware.logging_middleware import LogRequestMiddleware
from lume.router import router as lume_router
from onboarder.router import router as onboarder_router

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL),
    format='[%(levelname)s] %(asctime)s <%(name)s.%(module)s: %(lineno)s>    %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    handlers=[
        logging.FileHandler(settings.LOG_FILE_NAME),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="Schnelliot API",
    description="FastAPI conversion of Django tb_service project",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=settings.CORS_ALLOW_METHODS,
    allow_headers=settings.CORS_ALLOW_HEADERS,
)

# Add custom logging middleware
app.add_middleware(LogRequestMiddleware)

# Include routers
app.include_router(lume_router, prefix="/api")
app.include_router(onboarder_router, prefix="/onboard")

# Mount static files
app.mount("/Media", StaticFiles(directory="Media"), name="media")

@app.get("/")
async def root():
    return {"message": "Schnelliot FastAPI Service"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "message": "FastAPI server is running"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
