"""
"""

import json

import requests

from onboarder import tb_url

__author__ = 'boopathi'


class TBConnection:

    def __init__(self):
        """

        """

    @staticmethod
    def get_tb_token(request, tb_user_data=''):
        try:
            tb_auth_token = request.session['tb_token']
            tb_refresh_token = request.session['tb_refresh']
            # if tb_auth_token is None:
            #	try:
            #                url = tb_url + "/auth/login"
            #                header = {'Content-Type': 'application/json', 'Accept': 'application/json'}
            #                tb_get_token_response = requests.post(url, headers=header, data=tb_user_data, timeout=30)
            #                print "tb_get_token_response", tb_get_token_response
            #                tb_token_json_data = json.loads(tb_get_token_response.text)
            #                tb_auth_token = tb_token_json_data.get('token')
            #                tb_refresh_token = tb_token_json_data.get('refreshToken')
            #                request.session['tb_token'] = tb_auth_token
            #                request.session['tb_refresh'] = tb_refresh_token
            #                return tb_token_json_data
            #        except Exception, e:
            #		print 'get_tb_token login12  ---> ', str(e)
            # else:
            tb_token_json_data = {'token': tb_auth_token, 'refreshtoken': tb_refresh_token}
            print("get tb token :" + str(tb_token_json_data))
            return tb_token_json_data
        except Exception as e:
            print("get_tb_token ses Err --> ", str(e))
            print("New session and create token")
            for i in range(2):
                try:
                    url = tb_url + "/auth/login"
                    header = {'Content-Type': 'application/json', 'Accept': 'application/json'}
                    tb_get_token_response = requests.post(url, headers=header, data=tb_user_data, timeout=60)
                    print("tb_get_token_response", tb_get_token_response)
                    tb_token_json_data = json.loads(tb_get_token_response.text)
                    tb_auth_token = tb_token_json_data.get('token')
                    tb_refresh_token = tb_token_json_data.get('refreshToken')
                    request.session['tb_token'] = tb_auth_token
                    request.session['tb_refresh'] = tb_refresh_token
                    return tb_token_json_data
                except Exception as e:
                    print('get_tb_token login  ---> ', str(e))
                    continue

    @staticmethod
    def refresh_tb_token(request, tb_api_url, url_method, tb_api_default_url, tb_user_data, tb_api_data):
        try:
            url = tb_api_default_url + "/auth/login"
            header = {'Content-Type': 'application/json', 'Accept': 'application/json'}
            tb_get_token_response = requests.post(url, headers=header, data=tb_user_data, timeout=60)
            print("refresh_tb_token", tb_get_token_response)
            tb_token_json_data = json.loads(tb_get_token_response.text)
            tb_auth_token = tb_token_json_data.get('token')
            tb_refresh_token = tb_token_json_data.get('refreshToken')
            request.session['tb_token'] = tb_auth_token
            request.session['tb_refresh'] = tb_refresh_token
            # run the failed step data
            header = {'Content-type': 'application/json', 'Accept': '*/*', 'X-Authorization': 'Bearer ' + tb_auth_token}
            if url_method == 'post':
                tb_api_response = requests.post(tb_api_url, headers=header, data=tb_api_data, timeout=60)
            elif url_method == 'delete':
                tb_api_response = requests.delete(tb_api_url, headers=header, data=tb_api_data, timeout=60)
            else:
                tb_api_response = requests.get(tb_api_url, headers=header, data=tb_api_data, timeout=60)
            return tb_api_response
        except Exception as e:
            print('refresh_tb_token Err  ---> ', str(e))

    @staticmethod
    def tb_logout_token(header=None):
        try:
            url = tb_url + "/auth/logout"
            header = header
            tb_get_token_response = requests.post(url, headers=header, data='', timeout=60)
            print("tb_logout_response --> ", tb_get_token_response)
            return 'ok'
        except Exception as e:
            print("tb_logout_Error --> ", str(e))
            return 'fail'
