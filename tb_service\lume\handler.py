import json

import requests
from django.http import HttpResponse

from lume.__init__ import *


class RequestResponseHandler:
    def __init__(self):
        self.response = {"status": 200}

    def update_service_response(self, response) -> None:
        self.response = response

    def get_service_response(self) -> json:
        return json.dumps(self.response)

    def update_error_response(self, error: str) -> None:
        previous_response = json.loads(self.get_service_response())
        if previous_response.get("status") == 200:
            self.update_service_response({"status": 500, "message": error})

    @staticmethod
    def post_request(url: str, header: dict, data: str, instance: object) -> list or dict:
        try:
            logger.debug(url)
            get_post_response = requests.post(url=url, headers=header, data=data, timeout=30)
            response = RequestResponseHandler.is_success(get_post_response)
            instance.update_service_response(response)
            return response
        except Exception as e:
            logger.exception("post method failed %s" % str(e))
        return None

    @staticmethod
    def get_request(url: str, header: dict, data: str, instance: object) -> list or dict:
        try:
            logger.debug(url)
            get_response = requests.get(url=url, headers=header, data=data, timeout=30)
            response = RequestResponseHandler.is_success(get_response)
            instance.update_service_response(response)
            return response
        except Exception as e:
            logger.exception("get method failed %s" % str(e))
        return None

    @staticmethod
    def delete_request(url: str, header: dict, data: str, instance: object) -> list or dict:
        try:
            get_delete_response = requests.delete(url=url, headers=header, data=data, timeout=30)
            response = RequestResponseHandler.is_success(get_delete_response)
            instance.update_service_response(response)
            return response
        except Exception as e:
            logger.exception("delete method failed %s" % str(e))
        return None

    @staticmethod
    def is_success(response: dict or str) -> list or dict:
        success_response = {}
        try:
            if response.status_code == 403:
                success_response = {"status": response.status_code}
            success_response = json.loads(response.text)
        except Exception as e:
            success_response = {"status": 200}
        return success_response


def token_validator(func):
    def validate(request, *args, **kwargs):
        if "token" in request.headers:
            return func(request, *args, **kwargs)
        else:
            return HttpResponse(json.dumps({"status": 1000, "message": "Token not found"}))

    return validate


def validate_keys(required_keys):
    def key_validation(func):
        def validate(request, *args, **kwargs):
            request_params = json.loads(request.body)
            missing_keys = [key for key in required_keys if key not in request_params]
            if missing_keys:
                return HttpResponse(json.dumps({"status": 1000, "message": f"Missing keys {', '.join(missing_keys)}"}))
            return func(request, *args, **kwargs)

        return validate

    return key_validation


def method_validate(required_method):
    def method_validator(func):
        def validate(request, *args, **kwargs):
            if required_method not in request.method:
                return HttpResponse(json.dumps({"status": 405, "message": "The request method is not allowed"}))
            return func(request, *args, **kwargs)

        return validate

    return method_validator


def request_parameter_validate():
    def method_validator(func):
        def validate(request, *args, **kwargs):
            response = {"status": 400, "message": "Invalid request body"}
            if request.body:
                request_params = json.loads(request.body)
                if request_params:
                    pass
                else:
                    return HttpResponse(json.dumps(response))
            else:
                return HttpResponse(json.dumps(response))
            return func(request, *args, **kwargs)

        return validate

    return method_validator
