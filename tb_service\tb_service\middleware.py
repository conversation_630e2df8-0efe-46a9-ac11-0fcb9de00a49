import logging

logger = logging.getLogger()


class LogRequestMiddleware:
    """
    Django middleware to log the URL and parameters for GET and POST requests.
    Add 'path.to.middleware.LogRequestMiddleware' to your MIDDLEWARE setting.
    """

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        method = request.method.upper()
        url = request.get_full_path()
        url_list = str(url).split('api')

        try:
            url_list = ['sms/send/', 
                        'ghmc/tran_ghmc/', 
                        'ghmc/tran_ap/', 
                        'cochin/lamp/failure/']
            if url[1] not in url_list:
            # Extract parameters based on HTTP method
                if method == 'GET':
                    params = request.GET.dict()
                elif method == 'POST':
                    params = request.POST.dict()
                else:
                    params = {}

                # Log to both logger and console
                logger.debug(f"{method} {url}")
        except: ...

        # Continue processing
        response = self.get_response(request)
        return response
