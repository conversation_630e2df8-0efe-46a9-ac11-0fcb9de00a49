import time
import logging

logging.basicConfig(filename='script.log', level=logging.INFO, format='%(asctime)s %(message)s')

print("<PERSON>rip<PERSON> running")
logging.info("Scrip<PERSON> running")

# Create dummy file
filename = "dummy_file.txt"
content = "This is a dummy file created for testing purposes.\nTime: " + time.ctime()

with open(filename, "w") as f:
    f.write(content)

print(f"Dummy file '{filename}' created.")
logging.info(f"Dummy file '{filename}' created.")

print("Going to sleep for 10 seconds...")
logging.info("Going to sleep for 10 seconds...")
time.sleep(10)

print("<PERSON><PERSON><PERSON> completed.")
logging.info("<PERSON><PERSON><PERSON> completed.")
