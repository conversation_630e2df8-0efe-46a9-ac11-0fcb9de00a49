"""
Request/Response handler for FastAPI
Converted from Django RequestResponseHandler
"""

import json
import logging
import requests
from typing import Union, Dict, Any

logger = logging.getLogger(__name__)

class RequestResponseHandler:
    def __init__(self):
        self.response = {"status": 200}

    def update_service_response(self, response: Dict[str, Any]) -> None:
        self.response = response

    def get_service_response(self) -> Dict[str, Any]:
        return self.response

    def get_service_response_json(self) -> str:
        return json.dumps(self.response)

    def update_error_response(self, error: str) -> None:
        previous_response = self.get_service_response()
        if previous_response.get("status") == 200:
            self.update_service_response({"status": 500, "message": error})

    @staticmethod
    def post_request(url: str, header: dict, data: str, instance: 'RequestResponseHandler') -> Union[list, dict, None]:
        try:
            logger.debug(url)
            get_post_response = requests.post(url=url, headers=header, data=data, timeout=30)
            response = RequestResponseHandler.is_success(get_post_response)
            instance.update_service_response(response)
            return response
        except Exception as e:
            logger.exception("post method failed %s" % str(e))
        return None

    @staticmethod
    def get_request(url: str, header: dict, data: str, instance: 'RequestResponseHandler') -> Union[list, dict, None]:
        try:
            logger.debug(url)
            get_response = requests.get(url=url, headers=header, data=data, timeout=30)
            response = RequestResponseHandler.is_success(get_response)
            instance.update_service_response(response)
            return response
        except Exception as e:
            logger.exception("get method failed %s" % str(e))
        return None

    @staticmethod
    def delete_request(url: str, header: dict, data: str, instance: 'RequestResponseHandler') -> Union[list, dict, None]:
        try:
            get_delete_response = requests.delete(url=url, headers=header, data=data, timeout=30)
            response = RequestResponseHandler.is_success(get_delete_response)
            instance.update_service_response(response)
            return response
        except Exception as e:
            logger.exception("delete method failed %s" % str(e))
        return None

    @staticmethod
    def is_success(response) -> Union[list, dict]:
        success_response = {}
        try:
            if response.status_code == 403:
                success_response = {"status": response.status_code}
            success_response = json.loads(response.text)
        except Exception as e:
            success_response = {"status": 200}
        return success_response
