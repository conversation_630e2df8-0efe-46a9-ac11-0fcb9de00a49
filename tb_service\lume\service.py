import base64
import traceback
from urllib.parse import quote

import boto3

from lume.handler import *
from tb_service.service import *
from tb_service.settings import *
import time
import json
import uuid
import datetime
from datetime import datetime
from datetime import date
from google.cloud import pubsub_v1
from lume.__init__ import *

from Crypto.Signature import PKCS1_v1_5  #pip install pycrypto for py3.8 below and pip install pycryptodome for above
from Crypto.PublicKey import RSA
from Crypto.Hash import SHA1
import http.client
import urllib.parse
from datetime import datetime, timezone, date, timedelta

class TbAuthService(TbAuthController):

    def __init__(self, instance: object):
        self.instance = instance

    def login(self, user_name: str, password: str) -> dict:
        data = json.dumps({"username": user_name, "password": password})
        return RequestResponseHandler.post_request(url=BASE_URL + self.login_url,
                                                   header=self.header, data=data, instance=self.instance)

    def logout(self, header: str) -> None:
        self.header['X-Authorization'] = "Bearer " + header
        RequestResponseHandler.post_request(url=BASE_URL + self.logout_url, header=self.header, data='',
                                            instance=self.instance)

    def get_user_permissions(self, header: str) -> dict:
        url = BASE_URL + self.get_role_url
        self.header['X-Authorization'] = "Bearer " + header
        return RequestResponseHandler.get_request(url=url, header=self.header, data='', instance=self.instance)

    def get_user_details(self, header: str) -> dict:
        url = BASE_URL + self.get_user_details_url
        self.header['X-Authorization'] = "Bearer " + header
        return RequestResponseHandler.get_request(url=url, header=self.header, data='', instance=self.instance)

    def save_token(self, token: str) -> None:
        self.header['X-Authorization'] = token

    @staticmethod
    def get_roles(data: dict) -> bool:
        role_availability = False
        for role in data:
            if role.get("name").lower() == LUME_PRINT_QR_REQUIRED_ROLE:
                role_availability = True
        return role_availability


class EntityService(TbEntityController):
    def __init__(self, token: str, instance: RequestResponseHandler):
        self.header = {'content-type': 'application/json', 'X-Authorization': 'Bearer ' + token}
        self.relation_json = {"from": {}, "to": {}, "type": "", "typeGroup": "COMMON", "additionalInfo": {}}
        self.instance = instance

    @abstractmethod
    def get_entity_by_name(self, entity_type: str, name: str) -> dict:
        pass

    @abstractmethod
    def get_entity_by_id(self, entity_type: str, entity_id: str) -> dict:
        pass

    def get_entity_info_by_id(self, entity_type: str, entity_id: str) -> dict or None:
        url = BASE_URL + self.get_entity_info_by_id_url.format(entity_type=entity_type.lower(), entity_id=entity_id)
        return RequestResponseHandler.get_request(url=url, header=self.header, data='', instance=self.instance)

    def create_entity_relation(self, from_id: str, from_type: str, to_id: str, to_type: str,
                               relation_type: str, additional_info: dict = None) -> dict or None:
        try:
            # Creating relation
            _url = BASE_URL + self.create_relation
            self.relation_json['from'].update({"id": from_id, "entityType": from_type})
            self.relation_json['to'].update({"id": to_id, "entityType": to_type})
            self.relation_json['type'] = relation_type
            if additional_info:
                self.relation_json["additionalInfo"] = additional_info

            response = RequestResponseHandler.post_request(
                url=_url, header=self.header, data=json.dumps(self.relation_json), instance=self.instance)
            if response["status"] == 200:
                logger.debug("Relation created successfully for the given data: %s" % self.relation_json)
            return response
        except Exception as e:
            logger.exception("Failed to create a relation for from_id: %s and to_id: %s" % (from_id, to_id))
        return None

    def place_entity_under_group(
            self, entity_ids, owner_type, owner_id, group_type, group_name=None, group_id=None):
        try:

            if entity_ids is None or len(entity_ids) == 0:
                return None
            _entity_group_data = self.get_entity_group_by_entity_id(entity_id=entity_ids[0], entity_type=group_type)
            if _entity_group_data and len(_entity_group_data) > 1:
                for item in _entity_group_data[1:]:
                    self.remove_entity_under_group(entity_ids=entity_ids, owner_type=owner_type, group_type=group_type,
                                                   group_id=item.get("id"))
            _group_id = group_id
            if _group_id is None:
                logger.info(f"Placing Entity Under Group: Finding region groupId for region -> {group_name}")
                _url = BASE_URL + self.get_entity_groupId_from_owner_url.format(
                    owner_type=owner_type, owner_id=owner_id, group_type=group_type, group_name=group_name)
                _group_data = RequestResponseHandler.get_request(url=_url, header=self.header, data="",
                                                                 instance=self.instance)

                if "id" in _group_data and "id" in _group_data["id"]:
                    _group_id = _group_data["id"]["id"]

            if _group_id is None:
                return None

            _url = BASE_URL + self.ENTITY_GROUP_ADD_ENTITIES.format(entity_group_id=_group_id)
            logger.debug(f"Placing Entity Under Group: {group_name, _group_id}")
            response = RequestResponseHandler.post_request(
                url=_url, header=self.header, data=json.dumps(entity_ids), instance=self.instance)
            logger.debug(f"Placing Entity Under Group:  under group response {response}")
        except Exception as e:
            logger.exception(f"Error in placing Entity Under Group:  under group response {e}")

    def remove_entity_under_group(
            self, entity_ids, owner_type, group_type, group_name=None, group_id=None, owner_id=None):
        try:
            if entity_ids is None or len(entity_ids) == 0:
                return None
            _group_id = group_id
            if _group_id is None:
                logger.info(f"Removing Entity Under Group: Finding region groupId for region -> {group_name}")
                _url = BASE_URL + self.get_entity_groupId_from_owner_url.format(
                    owner_type=owner_type, owner_id=owner_id, group_type=group_type, group_name=group_name)
                _group_data = RequestResponseHandler.get_request(url=_url, header=self.header, data="",
                                                                 instance=self.instance)

                if "id" in _group_data and "id" in _group_data["id"]:
                    _group_id = _group_data["id"]["id"]

            if _group_id is None:
                return None

            _url = BASE_URL + self.ENTITY_GROUP_DELETE_ENTITIES.format(entity_group_id=_group_id)
            logger.debug(f"Removing Entity Under Group: {group_name, _group_id}")
            response = RequestResponseHandler.post_request(
                url=_url, header=self.header, data=json.dumps(entity_ids), instance=self.instance)
            logger.debug(f"Removing Entity Under Group:  under group response {response}")
        except Exception as e:
            logger.exception(f"Error in Removing Entity Under Group:  under group response {e}")

    def get_entity_group_by_entity_id(self, entity_id, entity_type):
        _url = BASE_URL + self.get_entity_groupId_by_entityId_url.format(entity_type=entity_type, entity_id=entity_id)
        return RequestResponseHandler.get_request(url=_url, header=self.header, data="",
                                                  instance=self.instance)


class DeviceService(EntityService):

    def __init__(self, token: str, instance: RequestResponseHandler):
        super().__init__(token, instance)

    def save_token(self, token: str) -> None:
        self.header['X-Authorization'] = token

    def get_entity_by_name(self, entity_type: str, name: str) -> dict or None:
        get_response = {"status": 500}
        url = BASE_URL + self.entity_by_name_url.format(entity_type=entity_type.lower(),
                                                        query_name="deviceName", entity_name=quote(name))
        get_response = RequestResponseHandler.get_request(url=url, header=self.header, data='', instance=self.instance)
        if get_response.get("status") and get_response.get("status") not in DEVICE_FAILURE_STATUS_HANDLER:
            get_response = self.get_entity_by_id(entity_type=entity_type, entity_id=quote(name))
        return get_response

    def get_entity_by_id(self, entity_type: str, entity_id: str) -> dict or None:
        url = BASE_URL + self.entity_by_id_url.format(entity_type=entity_type.lower(), entity_id=entity_id)
        return RequestResponseHandler.get_request(url=url, header=self.header, data='', instance=self.instance)

    def get_entity_attribute(self, entity_type: str, entity_id: str, scope: str, keys: str) -> list or None:
        url = BASE_URL + self.entity_attribute_url.format(entity_type=entity_type,
                                                          entity_id=entity_id, scope=scope, keys=keys)
        return RequestResponseHandler.get_request(url=url, header=self.header, data='', instance=self.instance)

    def update_entity_attribute(self, entity_type: str, entity_id: str, scope: str, data: str) -> list or None:
        url = BASE_URL + self.update_entity_attribute_url.format(entity_type=entity_type, entity_id=entity_id,
                                                                 scope=scope)
        response = RequestResponseHandler.post_request(url=url, header=self.header, data=data, instance=self.instance)
        return response

    def delete_entity_attribute(self, entity_type: str, entity_id: str, scope: str, keys: str) -> list or None:
        url = BASE_URL + self.delete_entity_attribute_url.format(entity_type=entity_type, entity_id=entity_id,
                                                                 scope=scope, keys=keys)
        return RequestResponseHandler.delete_request(url=url, header=self.header, data="", instance=self.instance)

    def get_entity_to_relation_info(self, to_id: str, to_type: str) -> list:
        url = BASE_URL + self.get_to_relation_info.format(to_id=to_id, to_type=to_type)
        return RequestResponseHandler.get_request(url=url, header=self.header, data="", instance=self.instance)

    def remove_entity_relation(self, from_id: str, from_type: str, to_id: str, to_type: str,
                               relation_type: str, additional_info: dict = None) -> dict:
        url = BASE_URL + self.remove_relation.format(from_id=from_id, from_type=from_type, to_id=to_id,
                                                     to_type=to_type, relation_type=relation_type)
        if additional_info:
            return RequestResponseHandler.delete_request(url=url, header=self.header, data=json.dumps(additional_info),
                                                         instance=self.instance)
        else:
            return RequestResponseHandler.delete_request(url=url, header=self.header, data="",
                                                         instance=self.instance)

    def get_from_relations_info(self, from_id: str, from_type: str) -> list:
        url = BASE_URL + self.get_all_entities_url.format(from_id=from_id, from_type=from_type)
        return RequestResponseHandler.get_request(url=url, header=self.header, data='', instance=self.instance)

    def find_entities_by_query(self, query: dict) -> dict:
        url = BASE_URL + self.entity_query_find_url
        return RequestResponseHandler.post_request(url=url, header=self.header, data=json.dumps(query),
                                                   instance=self.instance)

    def device_search(self, entity_type: str, pagesize: int, page: int, textsearch: str or int) -> dict:
        url = BASE_URL + self.entity_search_url.format(entity_type=entity_type.lower(), pagesize=pagesize, page=page,
                                                       textsearch=textsearch)
        return RequestResponseHandler.get_request(url=url, header=self.header, data='', instance=self.instance)

    def device_control(self, device_id: str, data: str) -> dict:
        url = BASE_URL + self.on_off_control_url.format(device_id=device_id)
        return RequestResponseHandler.post_request(url=url, header=self.header, data=data, instance=self.instance)

    def get_and_remove_to_relations(self, to_id: str, to_type: str, removal_type: str, removal: bool = False,
                                    additional_info: dict = None) -> list:
        get_to_relation = self.get_entity_to_relation_info(to_id=to_id, to_type=to_type)
        if removal:
            if additional_info:
                self.remove_device_relations(device_relations=get_to_relation,
                                             related_id=to_id, related_type=to_type, removal_type=removal_type,
                                             additional_info=additional_info)
            else:
                self.remove_device_relations(device_relations=get_to_relation,
                                             related_id=to_id, related_type=to_type, removal_type=removal_type)
        return get_to_relation

    def get_and_remove_from_relations(self, from_id: str, from_type: str, removal_type: str,
                                      removal: bool = False) -> list:
        get_from_relation = self.get_from_relations_info(from_id=from_id, from_type=from_type)
        if removal:
            self.remove_device_from_relations(device_relations=get_from_relation,
                                              related_id=from_id, related_type=from_type, removal_type=removal_type)
        return get_from_relation

    def remove_device_relations(self, device_relations: list, related_id: str, related_type: str,
                                removal_type: str, additional_info: dict = None) -> None:
        for relation in device_relations:
            if additional_info:
                self.remove_entity_relation(
                    from_id=relation.get(removal_type).get("id"),
                    from_type=relation.get(removal_type).get("entityType"),
                    to_id=related_id, to_type=related_type, relation_type=relation.get("type"),
                    additional_info=additional_info)
            else:
                self.remove_entity_relation(
                    from_id=relation.get(removal_type).get("id"),
                    from_type=relation.get(removal_type).get("entityType"),
                    to_id=related_id, to_type=related_type, relation_type=relation.get("type"))

    def remove_device_from_relations(self, device_relations: list, related_id: str, related_type: str,
                                     removal_type: str) -> None:
        for relation in device_relations:
            self.remove_entity_relation(
                from_id=related_id, from_type=related_type,
                to_id=relation.get(removal_type).get("id"), to_type=relation.get(removal_type).get("entityType"),
                relation_type=relation.get("type"))

    def get_all_to_relations(self, data: dict) -> list:
        ward_details = []
        relation_query = self.device_relations(entity_id=data.get('id'), entity_type=ENTITY_TYPE[1],
                                               direction=ENTITY_RELATIONS[1], relation_type=RELATION_TYPES[0])
        relation_data = self.find_entities_by_query(query=relation_query)
        if relation_data.get('data'):
            ward_details = self.construct_ward_response(data=relation_data['data'])
        return ward_details

    def change_owner(self, owner_type: str, owner_id: str, entity_id: str, entity_type: str = ENTITY_TYPE[0]):
        url = BASE_URL + self.change_ownership_url.format(owner_type=owner_type, owner_id=owner_id,
                                                          entity_id=entity_id, entity_type=entity_type)
        return RequestResponseHandler.post_request(url=url, header=self.header, data="", instance=self.instance)

    def upload_image(self, img_data: str, device_name: str, folder: str) -> None:
        if len(img_data) > 0:
            file_name = str(device_name)
            # file_name = "test_aws.png"
            file_full_path = str(BASE_DIR) + "/" + str(MEDIA_URL) + "/" + file_name
            if self.save_image_as_file(base_data=img_data, filename=file_full_path):
                self.instance.update_service_response(self.store_image_to_aws(bucket_type=AWS_BUCKET_TYPE,
                                                                              region_name=AWS_BUCKET_REGION,
                                                                              access_key=AWS_BUCKET_ACCESS_KEY,
                                                                              secret_key=AWS_BUCKET_SECRET_KEY,
                                                                              bucket_folder=AWS_BUCKET_LUMINATOR_FOLDER_NAME,
                                                                              file_name=file_name,
                                                                              file_stored_path=file_full_path))
            else:
                self.instance.update_service_response({"status": 1000, "message": "Error while upload image"})

    def get_and_remove_from_and_to_relations(self, entity_id: str, entity_type: str,
                                             addition_info: dict = None) -> list:
        from_relation = self.get_and_remove_from_relations(from_id=entity_id, from_type=entity_type,
                                                           removal_type=ENTITY_RELATIONS[1].lower(), removal=True)
        to_relations = self.get_and_remove_to_relations(to_id=entity_id, to_type=entity_type,
                                                        removal_type=ENTITY_RELATIONS[0].lower(), removal=True)
        return [from_relation, to_relations]

    def create_from_and_to_relations(self, from_relation: list, to_relation: list, entity_id: str, entity_type: str):
        self.construct_and_create_relations(data=from_relation, related_type=ENTITY_RELATIONS[0], entity_id=entity_id,
                                            entity_type=entity_type)
        self.construct_and_create_relations(data=to_relation, related_type=ENTITY_RELATIONS[1], entity_id=entity_id,
                                            entity_type=entity_type)

    def create_device(self, entity_type: str, data: dict):
        url = BASE_URL + self.create_or_update_entity_url.format(entity_type=entity_type.lower())
        return RequestResponseHandler.post_request(url=url, header=self.header, data=json.dumps(data),
                                                   instance=self.instance)

    def create_or_update_entity(self, entity_type: str, data: dict) -> dict:
        validate_asset = self.get_entity_by_name(entity_type=entity_type, name=data.get('name'))
        if validate_asset.get("status") == 404:
            return self.create_device(entity_type=entity_type, data=data)
        return {"status": 1000, "message": "CCMS/Gw already present", "details": validate_asset}

    def construct_and_create_relations(self, data: list, related_type: str, entity_id: str, entity_type: str):
        for relation in data:
            if related_type.lower() == ENTITY_RELATIONS[1].lower():
                self.create_entity_relation(from_id=relation.get("from").get("id"),
                                            from_type=relation.get("from").get("entityType"), to_id=entity_id,
                                            to_type=entity_type,
                                            relation_type=relation.get("type"))
            else:
                self.create_entity_relation(from_id=entity_id,
                                            from_type=entity_type, to_id=relation.get("to").get("id"),
                                            to_type=relation.get("to").get("entityType"),
                                            relation_type=relation.get("type"))

    @staticmethod
    def ilm_test_attributes() -> dict:
        return {"testResults": {"brightness_100": False, "brightness_70": False, "brightness_50": False,
                                "brightness_30": False,
                                "brightness_0": False, "flash": False, "rtc": False, "error_count": 0,
                                "start_ts": int(time.time()), "end_ts": 0, "jig_number": ""}}

    @staticmethod
    def get_lume_ilm_packet(ilm_details: dict, attributes: list, relations: list, entity_type: str) -> dict:
        try:
            attribute_json = DeviceService.ilm_test_attributes()
            device_relations = []
            jig_number = ""
            if entity_type.lower() in (DEVICE_TYPES[0], DEVICE_TYPES[3], DEVICE_TYPES[4], DEVICE_TYPES[5]):
                device_relations = relations
            else:
                jig_number = [relation.get("fromName") for relation in relations]
                jig_number = jig_number[0] if len(jig_number) > 0 else ""
            for attribute in attributes:
                attribute_json[attribute.get('key')] = attribute.get('value')
            attribute_json["testResults"]["jig_number"] = jig_number
            ilm_device_pkt = {"deviceDetails": {
                "deviceId": ilm_details.get("id").get("id"),
                "deviceType": ilm_details.get("type"),
                "name": ilm_details.get("name"),
                "active": attribute_json.get("active", False),
                "dimmable": attribute_json.get("dimmable", False),
                "PDI": attribute_json.get("PDI", False),
                "state": attribute_json.get("state", "TESTABLE"),
                "condition": attribute_json.get("condition", "new"),
                "wardName": attribute_json.get("wardName", ""),
                "zoneName": attribute_json.get("zoneName", ""),
                "region": attribute_json.get("region", ""),
                "qrCount": attribute_json.get("qrCount", 0),
                "jigNumber": jig_number,
                "lastActivityTime": attribute_json.get("lastActivityTime", 0),
                "testResults": attribute_json.get("testResults"),
                "relations": device_relations
            }}
            return ilm_device_pkt
        except Exception as e:
            return {"status": 1000, "message": "Internal Error"}

    @staticmethod
    def validate_test_initiate(request_params: dict, attributes: dict) -> dict or bool:
        if ILM_TEST_QUERY_PARAMS[0] in request_params and ILM_TEST_QUERY_PARAMS[2] in request_params:
            if request_params.get(ILM_TEST_QUERY_PARAMS[2]) == "":
                attributes.update(DeviceService.construct_attributes(
                    request_attributes=request_params, invalid_keys=ILM_TEST_QUERY_PARAMS))
            return attributes
        else:
            return False

    @staticmethod
    def construct_attributes(request_attributes: dict, invalid_keys: list = None, valid_keys: list = None) -> dict:
        attributes = {}
        if invalid_keys is not None:
            for attribute in request_attributes.keys():
                if attribute not in invalid_keys:
                    attributes[attribute] = request_attributes.get(attribute)
        elif valid_keys is not None:
            for attribute in request_attributes.keys():
                if attribute in valid_keys:
                    attributes[attribute] = request_attributes.get(attribute)
        return DeviceService.change_attribute_type(data=attributes)

    @staticmethod
    def change_attribute_type(data: dict) -> dict:
        bool_keys = {"false": False, "true": True}
        _attr = None
        try:
            request_json = {}
            for attr in data.keys():
                try:
                    _attr = attr
                    if attr in ILM_ATTRIBUTE_TYPE_CONVERT_BOOL:
                        request_json[attr] = bool_keys[data.get(attr)]
                    elif attr in ILM_ATTRIBUTE_TYPE_CONVERT_INT:
                        request_json[attr] = int(data.get(attr))
                    elif attr in ILM_ATTRIBUTE_TYPE_CONVERT_DOUBLE:
                        request_json[attr] = float(data.get(attr))
                    else:
                        request_json[attr] = data.get(attr)
                except Exception as e1:
                    # dimmalbe shows 00 as value,that's frequently throws error
                    request_json[attr] = data.get(attr)
            return request_json
        except Exception as e:
            logger.exception("can't Change attribute type -%s - %s" % (_attr, str(e)))
            return data

    @staticmethod
    def device_level_entity(entity_id: str, entity_type: str) -> dict:
        try:
            entity_query = {
                "entityFields": [
                    {
                        "type": "ENTITY_FIELD",
                        "key": "name"
                    },
                    {
                        "type": "ENTITY_FIELD",
                        "key": "type"
                    },
                    {
                        "type": "ENTITY_FIELD",
                        "key": "label"
                    }],
                "latestValues": [
                    {
                        "key": "state",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "condition",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "latitude",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "longitude",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "slatitude",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "slongitude",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "wardName",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "zoneName",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "regionName",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "landmark",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "location",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "commissioned",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "lastActivityTime",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "active",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "lampWatts",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "accuracy",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "lamp",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "region",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "installedBy",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "installedOn",
                        "type": "SERVER_ATTRIBUTE"
                    },
                ],
                "entityFilter": {
                    "type": "singleEntity",
                    "singleEntity": {
                        "id": entity_id,
                        "entityType": entity_type
                    }
                },
                "pageLink": {
                    "dynamic": True, "page": 0, "pageSize": 100,
                    "sortOrder": {
                        "direction": "ASC"
                    },
                    "textSearch": ""
                }
            }
            return entity_query
        except Exception as e:
            logger.exception("can't create a device level query - %s" % str(e))

    @staticmethod
    def asset_level_device_entity(entity_id: str, device_type: list, relation_type: str, entity_type: str) -> dict:
        try:
            entity_query = {
                "entityFields": [
                    {
                        "type": "ENTITY_FIELD",
                        "key": "name"
                    },
                    {
                        "type": "ENTITY_FIELD",
                        "key": "type"
                    },
                    {
                        "type": "ENTITY_FIELD",
                        "key": "label"
                    }],
                "latestValues": [],
                "entityFilter": {
                    "type": "deviceSearchQuery",
                    "rootEntity": {
                        "entityType": entity_type,
                        "id": entity_id
                    },
                    "direction": "FROM", "fetchLastLevelOnly": True, "relationType": relation_type,
                    "deviceTypes": device_type
                },
                "pageLink": {
                    "dynamic": True, "page": 0, "pageSize": 10000,
                    "sortOrder": {'key': {'key': 'name', 'type': 'ENTITY_FIELD'}, 'direction': 'ASC'},
                    "textSearch": ""
                }
            }
            return entity_query
        except Exception as e:
            logger.exception("couldn't create a asset level query - %s" % str(e))

    @staticmethod
    def device_relations(entity_id: str, entity_type: str, direction: str, relation_type: str) -> dict:
        try:
            entity_query = {
                "entityFields": [
                    {
                        "type": "ENTITY_FIELD",
                        "key": "name"
                    },
                    {
                        "type": "ENTITY_FIELD",
                        "key": "id"
                    },
                    {
                        "type": "ENTITY_FIELD",
                        "key": "type"
                    },
                    {
                        "type": "ENTITY_FIELD",
                        "key": "label"
                    },
                ],
                "latestValues": [
                    {
                        "key": "poleSchema",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "state",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "condition",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "latitude",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "longitude",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "wardName",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "zoneName",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "region",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "landmark",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "commissioned",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "lampWatts",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "accuracy",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "installedBy",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "installedOn",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "lastActivityTime",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "active",
                        "type": "SERVER_ATTRIBUTE"
                    },
                ],
                "entityFilter": {
                    "type": "relationsQuery",
                    "rootEntity": {
                        "entityType": entity_type,
                        "id": entity_id
                    },
                    "direction": direction,
                    "fetchLastLevelOnly": False, "maxLevel": 1,
                    "filters": [
                        {
                            "relationType": relation_type,
                            "entityTypes": ["DEVICE", "ASSET"]
                        }
                    ]
                },
                "pageLink": {
                    "dynamic": True, "page": 0, "pageSize": 100,
                    "sortOrder": {
                        "direction": "ASC"
                    },
                    "textSearch": ""
                }
            }
            return entity_query
        except Exception as e:
            logger.exception("can't create a relation query - %s" % str(e))

    @staticmethod
    def gw_device_relations(entity_id: str, entity_type: str, direction: str, relation_type: str) -> dict:
        try:
            entity_query = {
                "entityFields": [
                    {
                        "type": "ENTITY_FIELD",
                        "key": "name"
                    },
                    {
                        "type": "ENTITY_FIELD",
                        "key": "id"
                    },
                    {
                        "type": "ENTITY_FIELD",
                        "key": "type"
                    },
                    {
                        "type": "ENTITY_FIELD",
                        "key": "label"
                    },
                ],
                "latestValues": [
                    {
                        "key": "simNo",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "ebMeterNo",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "phase",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "state",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "condition",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "slatitude",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "slongitude",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "wardName",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "zoneName",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "region",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "location",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "landmark",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "lastActivityTime",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "accuracy",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "installedBy",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "installedOn",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "active",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "type": "TIME_SERIES",
                        "key": "meterReadingOffset"
                    },
                    {
                        "key": "ebMeterReplacedBy",
                        "type": "TIME_SERIES"
                    },
                    {
                        "key": "ebMeterReplacedOn",
                        "type": "TIME_SERIES"
                    }
                ],
                "entityFilter": {
                    "type": "relationsQuery",
                    "rootEntity": {
                        "entityType": entity_type,
                        "id": entity_id
                    },
                    "direction": direction,
                    "fetchLastLevelOnly": False, "maxLevel": 1,
                    "filters": [
                        {
                            "relationType": relation_type,
                            "entityTypes": ["DEVICE", "ASSET"]
                        }
                    ]
                },
                "pageLink": {
                    "dynamic": True, "page": 0, "pageSize": 100,
                    "sortOrder": {
                        "direction": "ASC"
                    },
                    "textSearch": ""
                }
            }
            return entity_query
        except Exception as e:
            logger.exception("can't create a relation query - %s" % str(e))

    @staticmethod
    def construct_find_query(entity_type: str, text_search: str) -> dict:
        try:
            data = {
                "entityFilter": {
                    "type": "entityName",
                    "entityType": entity_type,
                    "entityNameFilter": ""
                },
                "entityFields": [
                    {
                        "type": "ENTITY_FIELD",
                        "key": "name"
                    },
                    {
                        "type": "ENTITY_FIELD",
                        "key": "type"
                    }
                ],
                "latestValues": [],
                "keyFilters": [{
                    "key": {
                        "type": "ENTITY_FIELD",
                        "key": "name"
                    },
                    "valueType": "STRING",
                    "predicate": {
                        "operation": "CONTAINS",
                        "value": {
                            "defaultValue": text_search,
                            "dynamicValue": None
                        },
                        "type": "STRING"
                    }
                }
                ],
                "pageLink": {
                    "page": 0,
                    "pageSize": 100,
                    "sortOrder": {
                        "key": {
                            "key": "name",
                            "type": "ENTITY_FIELD"
                        },
                        "direction": "ASC"
                    },
                    "textSearch": ""
                }
            }
            return data
        except Exception as e:
            logger.exception("couldn't create a find query - %s" % str(e))

    @staticmethod
    def construct_ilm_gw_details(data: list) -> dict:
        """
        type: Type Of the Device in (ilm, gw, nic)
        """
        device_details = {}
        for details in data:
            _entity_type = details.get("latest").get("ENTITY_FIELD").get("type").get("value")
            device_details = {
                "name": details.get("latest").get("ENTITY_FIELD").get("name").get("value"),
                "id": details.get("entityId").get("id"),
                "type": _entity_type,
                "label": details.get("latest").get("ENTITY_FIELD").get("label").get("value")
                              }
            if "SERVER_ATTRIBUTE" in details.get("latest"):
                if _entity_type in (DEVICE_TYPES[0], DEVICE_TYPES[4]):
                    device_details["type"] = DEVICE_TYPES[0]
                    for attribute in GW_INSTALLATION_ATTRIBUTES + DEVICE_FIND_QUERY_ATTRIBUTE_RESPONSE_KEYS[12:14]:
                        device_details[attribute] = details.get("latest").get("SERVER_ATTRIBUTE").get(attribute).get(
                            "value")
                elif _entity_type in (DEVICE_TYPES[1], DEVICE_TYPES[2]):
                    for attribute in ILM_INSTALLATION_ATTRIBUTES + DEVICE_FIND_QUERY_ATTRIBUTE_RESPONSE_KEYS[12:14]:
                        device_details[attribute] = details.get("latest").get("SERVER_ATTRIBUTE").get(attribute).get(
                            "value")
        return device_details

    @staticmethod
    def construct_return_device_response(device: dict = None, lp: dict = None, lamp: dict = None, gw: dict = None,
                                         ccms: dict = None, hub: dict = None, pole: dict = None) -> dict:
        if lamp and lp or lp and device or lp is not None or lamp is not None or device is not None:
            return {"ilm": device, "lightPoint": lp, "lamp": lamp, "pole": pole}
        elif gw is not None and ccms is None and hub is None:
            return {"gw": gw}
        elif ccms is not None and gw is not None:
            return {"gw": gw, "ccms": ccms}
        elif hub is not None and gw is not None:
            return {"gw": gw, "hub": hub}
        elif pole is not None:
            return {"pole": pole}

    @staticmethod
    def construct_ward_response(data: list) -> list:
        ward_details = []
        for details in data:
            ward_details.append({"name": details.get("latest").get("ENTITY_FIELD").get("name").get("value"),
                                 "id": details.get("entityId").get("id"),
                                 "type": details.get("latest").get("ENTITY_FIELD").get("type").get("value"),
                                 "label": details.get("latest").get("ENTITY_FIELD").get("label").get("value")})
        return ward_details

    @staticmethod
    def construct_response_data(asset_data: dict, device_data: dict) -> list:
        device_details = []
        for details in device_data:
            device_details.append({"name": details.get("latest").get("ENTITY_FIELD").get("name").get("value"),
                                   "id": details.get("entityId").get("id"),
                                   "type": details.get("latest").get("ENTITY_FIELD").get("type").get("value")})
        for data in asset_data:
            device_details.append({"name": data.get("latest").get("ENTITY_FIELD").get("name").get("value"),
                                   "id": data.get("entityId").get("id"),
                                   "type": data.get("latest").get("ENTITY_FIELD").get("type").get("value")})
        return device_details

    @staticmethod
    def relation_construct(data: list, attributes: bool = False) -> dict:
        """
        type: Type Of the Entity
        """
        try:
            device_detail = {}
            for details in data:
                _entity_type = details.get("latest").get("ENTITY_FIELD").get("type").get("value")
                device_detail = {"name": details.get("latest").get("ENTITY_FIELD").get("name").get("value"),
                                 "id": details.get("entityId").get("id"),
                                 "type": _entity_type,
                                 "label": details.get("latest").get("ENTITY_FIELD").get("label").get("value")
                                 }
                if attributes and "SERVER_ATTRIBUTE" in details.get("latest"):
                    _server_attributes = details.get("latest").get("SERVER_ATTRIBUTE")
                    if _entity_type in GW_ASSET_TYPES:
                        for attribute in CCMS_INSTALLATION_ATTRIBUTES + GW_DISPATCH_QUERY_PARAMS:
                            if attribute in _server_attributes:
                                device_detail[attribute] = _server_attributes.get(attribute).get("value")
                    elif details.get("latest").get("ENTITY_FIELD").get("type").get("value") == ILM_ASSET_TYPES[1]:
                        for attribute in LIGHT_POINT_INSTALLATION_ATTRIBUTES:
                            if attribute in _server_attributes:
                                device_detail[attribute] = _server_attributes.get(attribute).get("value")
            return device_detail
        except Exception as e:
            logger.exception("relation construct - %s" % str(e))

    @staticmethod
    def create_attributes(entity_type: str = None, ilm: str = None, lp: str = None, lamp: str = None,
                          pole_name: str = None) -> dict:
        attribute = {}
        if entity_type:
            if entity_type.lower() == DEVICE_TYPES[1].lower() or DEVICE_TYPES[2].lower():
                attribute = {"lightPoint": lp, "lamp": lamp, "poleId": pole_name}
            if entity_type.lower() == ILM_ASSET_TYPES[1].lower():
                attribute = {"ilm": ilm, "lamp": lamp, "poleId": pole_name, "state": "INSTALLED"}
            if entity_type.lower() == ILM_ASSET_TYPES[0].lower():
                attribute = {"ilm": ilm, "lightPoint": lp, "poleId": pole_name}
        return attribute

    @staticmethod
    def save_image_as_file(base_data: str, filename: str) -> bool:
        response = True
        try:
            imgdata = base64.b64decode(base_data)
            with open(filename, 'wb') as f:
                f.write(imgdata)
        except Exception as e:
            response = False
        return response

    @staticmethod
    def store_image_to_aws(bucket_type: str, region_name: str, access_key: str, secret_key: str, bucket_folder: str,
                           file_name: str, file_stored_path: str):
        response = {"status": 200, "message": "Image upload Successfully Completed"}
        try:
            user = boto3.client(bucket_type, region_name=region_name, aws_access_key_id=access_key,
                                aws_secret_access_key=secret_key)
            file_path = bucket_folder + '/' + file_name
            user.upload_file(file_stored_path, AWS_BUCKET_NAME, file_path)
            os.remove(file_stored_path)
            logger.info({"status": 200, "message": "Image upload Successfully Completed"})
        except Exception as e:
            response = {"status": 400, "message": "Error while upload image"}
        return response

    @staticmethod
    def device_construct_to_update(device_label: str, data: dict) -> dict:
        return {"id": {"entityType": ENTITY_TYPE[0], "id": data.get("deviceId")},
                "createdTime": data.get("createdTime"),
                "additionalInfo": "", "customerId": {"entityType": OWNER_TYPE[0], "id": data.get("customerId")},
                "name": data.get("name"), "type": data.get("deviceType"), "label": device_label,
                "ownerId": {"entityType": OWNER_TYPE[0], "id": data.get("customerId")}}

    @staticmethod
    def device_asset_server_attributes_construct(entity_id: str, entity_type: str,
                                                 server_attributes: dict) -> dict:
        return {"id": entity_id, "entity_type": entity_type, "SERVER_ATTRIBUTES": server_attributes}

    @staticmethod
    def send_datas_to_sqs(data: dict):
        try:
            sqs = boto3.client("sqs", region_name=AWS_INSTALL_SERVICE_REGION,
                               aws_access_key_id=AWS_INSTALL_SERVICE_ACCESS_KEY,
                               aws_secret_access_key=AWS_INSTALL_SERVICE_SECRET_KEY)
            queue_url = AWS_INSTALL_SERVICE_SQS_URL
            data_list = []
            if data is not None:
                if "ilm" in data.keys():
                    data_list.append(data["ilm"])
                if "lightPoint" in data.keys():
                    data_list.append(data["lightPoint"])
                if "lamp" in data.keys():
                    data_list.append(data["lamp"])
            body_data = {
                "construct_data": data_list
            }
            response = sqs.send_message(
                QueueUrl=queue_url,
                DelaySeconds=5,
                MessageBody=(
                    json.dumps(body_data)
                )
            )
            return response.get("ResponseMetadata").get("HTTPStatusCode")
        except Exception as e:
            logger.exception("Failed to Send Records:", str(e))
            traceback.print_exc()

    @staticmethod
    def install_ilm(request_params, asset_service, device_service, request_handler, customer_service):
        try:
            logger.info(f"\n\nInstalling ILM...")
            logger.debug(f"\n{request_params}")
            ward_id = request_params.get("wardId")
            img_data = request_params.get("auditImg")
            if request_params.get("ilm").get("name") != "":
                device_name = request_params.get("ilm").get("name")
                customer_id = request_params.get("customerId")
                pole_id = request_params.get("poleId")
                light_point_id = request_params.get("lightPointId")
                region = request_params.get("region")
                device_details = device_service.get_entity_by_name(entity_type=ENTITY_TYPE[0], name=device_name)
                ilm_device_id = device_details.get("id").get("id")
                default_customer_name = {}

                lp_relation = device_service.get_entity_to_relation_info(to_id=ilm_device_id,
                                                                         to_type=ENTITY_TYPE[0])
                if customer_id:
                    default_customer_name = customer_service.get_customer_by_id(
                                        owner_type=OWNER_TYPE[0], owner_id=customer_id)
                if lp_relation:
                    lp_id = lp_relation[0]["from"]["id"]
                    lp_attributes = {"ilm": "-"}
                    device_service.update_entity_attribute(entity_type=ENTITY_TYPE[1],
                                                           entity_id=lp_id, scope=ATTRIBUTE_SCOPES[1],
                                                           data=json.dumps(lp_attributes))
                device_service.change_owner(owner_type=OWNER_TYPE[0], owner_id=customer_id, entity_id=ilm_device_id)
                device_service.get_and_remove_to_relations(to_id=ilm_device_id, to_type=ENTITY_TYPE[0], removal=True,
                                                           removal_type=ENTITY_RELATIONS[0].lower())
                telemetry_data = {"ts": request_params.get("installedOn"),
                                  "values": {"auditImg": img_data, "pkt": 102, "activity": "Install"}}
                light_point_attributes = {}
                ilm_attributes = {}
                ilm_lamp_attributes = {}
                pole_data = {}
                if request_params.get("pole"):
                    pole_data = request_params.get("pole")

                # To find existing LightPoint if any without ILM
                _pole_id = request_params.get("poleId")
                # if _pole_id and (light_point_id is None or light_point_id == ""):
                #     # Fetching Installed Arm's count of a Pole
                #     _pole_relation = asset_service.get_relation_info_fromid(
                #         from_id=_pole_id, from_type=ENTITY_TYPE[1], relation_type=RELATION_TYPES[5])
                #     for item in _pole_relation:
                #         _lp_id = item.get("to", {}).get("id")
                #         _ilm_attr = asset_service.get_entity_attribute(
                #             entity_type=ENTITY_TYPE[1], entity_id=_lp_id, scope=ATTRIBUTE_SCOPES[1],
                #             keys="ilm")
                #         if not _ilm_attr or _ilm_attr[0].get('value') == '-':
                #             light_point_id = _lp_id
                #             break

                if light_point_id != "":
                    get_customer_info_from_lp = asset_service.get_entity_by_id(entity_type=ENTITY_TYPE[1],
                                                                               entity_id=light_point_id)
                    get_customer_info_from_lp["label"] = device_name
                    asset_service.create_asset(entity_type=ENTITY_TYPE[1], data=get_customer_info_from_lp)
                    asset_service.create_entity_relation(from_id=light_point_id, from_type=ENTITY_TYPE[1],
                                                         to_id=ilm_device_id, to_type=ENTITY_TYPE[0],
                                                         relation_type=RELATION_TYPES[1])
                    from_relation_query = device_service.device_relations(entity_id=light_point_id,
                                                                          entity_type=ENTITY_TYPE[1],
                                                                          direction=ENTITY_RELATIONS[0],
                                                                          relation_type=RELATION_TYPES[2])
                    from_relation_detail = device_service.find_entities_by_query(query=from_relation_query)
                    if from_relation_detail["data"]:
                        from_info = device_service.relation_construct(data=from_relation_detail["data"])
                        lamp_asset_id, lamp_asset_name = from_info.get("id"), from_info.get("name")
                        lamp_details = asset_service.get_entity_by_name(entity_type=ENTITY_TYPE[1],
                                                                        name=from_info.get("name"))
                        lamp_details["label"] = device_name
                        asset_service.create_asset(entity_type=ENTITY_TYPE[1], data=lamp_details)
                    else:
                        lamp_details = request_params.get("lamp")
                        if lamp_details:
                            lamp_det = "{manufacturer}-{wattage}-{type}-{dimmable}-{year}-{name}".format(
                                manufacturer=str(lamp_details.get("manufacturer")),
                                wattage=str(lamp_details.get("lampWatts")).zfill(4),
                                type=str(lamp_details.get("lampType")), dimmable=str(lamp_details.get("dimmable")),
                                year=str(lamp_details.get("year")), name=str(lamp_details.get("name")))
                            get_lamp = asset_service.get_entity_by_name(entity_type=ENTITY_TYPE[1], name=lamp_det)
                            if get_lamp.get("status") == 400 or get_lamp.get("status") == 404:
                                construct_new_lamp_asset = asset_service.create_lamp_asset(data=request_params,
                                                                                           detail=lamp_det)
                                create_lamp = asset_service.create_and_update_asset_name(
                                    details=construct_new_lamp_asset)
                                lamp_asset_id, lamp_asset_name = create_lamp.get("id"), create_lamp.get("name")
                                ilm_lamp_attributes.update(LAMP_INSTALLED_STATE_CONDITION)
                                ilm_lamp_attributes.update(device_service.construct_attributes(
                                    request_attributes=lamp_details, valid_keys=LAMP_INSTALLATION_ATTRIBUTES))
                                light_point_attributes.update(
                                    {LIGHT_POINT_INSTALLATION_ATTRIBUTES[9]: lamp_details.get("lampWatts")})
                            else:
                                lamp_asset_id, lamp_asset_name = get_lamp.get("id").get("id"), get_lamp.get("name")
                                device_service.get_and_remove_from_and_to_relations(entity_id=lamp_asset_id,
                                                                                    entity_type=ENTITY_TYPE[1])
                                ilm_lamp_attributes.update(INSTALLED_STATE)
                                if get_lamp.get("ownerId").get("id") != request_params.get("customerId"):
                                    asset_service.change_owner(owner_type=OWNER_TYPE[0],
                                                               owner_id=request_params.get("customerId"),
                                                               entity_id=lamp_asset_id)
                            ilm_lamp_attributes.update(device_service.construct_attributes(
                                request_attributes=request_params, valid_keys=LAMP_INSTALLATION_ATTRIBUTES[4:7]))
                        else:
                            construct_new_lamp_asset = asset_service.create_asset_entity(
                                request_param=ILM_ASSET_TYPES[0],
                                data=request_params,
                                label=device_name)
                            ilm_lamp_attributes.update(LAMP_INSTALLED_STATE_CONDITION)
                            ilm_lamp_attributes.update(device_service.construct_attributes(
                                request_attributes=request_params, valid_keys=LAMP_INSTALLATION_ATTRIBUTES[4:7]))
                            create_lamp = asset_service.create_and_update_asset_name(details=construct_new_lamp_asset)
                            lamp_asset_id, lamp_asset_name = create_lamp.get("id"), create_lamp.get("name")
                            lamp_det = lamp_asset_name
                        message = asset_service.construct_pubsub_lamp_install_report_data(
                            lamp_data=request_params, lamp_det=lamp_det, pole_data=request_params.get("pole"),
                            details=lamp_details, remove=False, img_name=img_data, customer_name=default_customer_name.get("name"))
                        asset_service.publish_lamp_data_to_pubsub(message=message)
                        asset_service.create_entity_relation(from_id=light_point_id, from_type=ENTITY_TYPE[1],
                                                             to_id=lamp_asset_id, to_type=ENTITY_TYPE[1],
                                                             relation_type=RELATION_TYPES[2],
                                                             additional_info={"lamp": message})
                    ilm_attributes.update(device_service.construct_attributes(
                        request_attributes=request_params, valid_keys=ILM_INSTALLATION_ATTRIBUTES))
                    ilm_add_attributes = device_service.create_attributes(
                        entity_type=device_details.get("type").lower(), lp=get_customer_info_from_lp.get("name"),
                        lamp=lamp_asset_name, pole_name=pole_data.get("name") if pole_data else "")
                    ilm_attributes.update(INSTALLED_STATE)
                    ilm_attributes.update(ilm_add_attributes)
                    light_point_add_attributes = device_service.create_attributes(
                        entity_type=ILM_ASSET_TYPES[1], ilm=device_name, lamp=lamp_asset_name,
                        pole_name=pole_data.get("name") if pole_data else "")
                    ilm_lamp_attributes.update(device_service.create_attributes(
                        ilm=device_name, entity_type=ILM_ASSET_TYPES[0], lp=get_customer_info_from_lp.get("name"),
                        pole_name=pole_data.get("name") if pole_data else ""))
                    asset_service.update_telemetry(entity_type=ENTITY_TYPE[0], entity_id=ilm_device_id,
                                                   data=telemetry_data)
                    asset_service.update_telemetry(entity_type=ENTITY_TYPE[1], entity_id=light_point_id,
                                                   data=telemetry_data)
                    asset_service.update_telemetry(entity_type=ENTITY_TYPE[1], entity_id=lamp_asset_id,
                                                   data=telemetry_data)

                    device_service.update_entity_attribute(entity_type=ENTITY_TYPE[0],
                                                           entity_id=ilm_device_id, scope=ATTRIBUTE_SCOPES[1],
                                                           data=json.dumps(ilm_attributes))
                    device_service.update_entity_attribute(entity_type=ENTITY_TYPE[1],
                                                           entity_id=light_point_id, scope=ATTRIBUTE_SCOPES[1],
                                                           data=json.dumps(light_point_add_attributes))
                    device_service.update_entity_attribute(entity_type=ENTITY_TYPE[1],
                                                           entity_id=lamp_asset_id, scope=ATTRIBUTE_SCOPES[1],
                                                           data=json.dumps(ilm_lamp_attributes))
                else:
                    construct_new_light_point_asset = asset_service.create_asset_entity(
                        request_param=ILM_ASSET_TYPES[1],
                        data=request_params,
                        label=device_name)

                    light_point = asset_service.create_and_update_asset_name(details=construct_new_light_point_asset)
                    light_point_id, light_point_name = light_point.get("id"), light_point.get("name")
                    asset_service.create_entity_relation(from_id=light_point_id, from_type=ENTITY_TYPE[1],
                                                         to_id=ilm_device_id, to_type=ENTITY_TYPE[0],
                                                         relation_type=RELATION_TYPES[1])
                    lamp_details = request_params.get("lamp")

                    if lamp_details:
                        lamp_det = "{manufacturer}-{wattage}-{type}-{dimmable}-{year}-{name}".format(
                            manufacturer=str(lamp_details.get("manufacturer")),
                            wattage=str(lamp_details.get("lampWatts")).zfill(4),
                            type=str(lamp_details.get("lampType")), dimmable=str(lamp_details.get("dimmable")),
                            year=str(lamp_details.get("year")), name=str(lamp_details.get("name")))
                        get_lamp = asset_service.get_entity_by_name(entity_type=ENTITY_TYPE[1], name=lamp_det)
                        if get_lamp.get("status") == 400 or get_lamp.get("status") == 404:
                            construct_new_lamp_asset = asset_service.create_lamp_asset(data=request_params,
                                                                                       detail=lamp_det)
                            create_lamp = asset_service.create_and_update_asset_name(details=construct_new_lamp_asset)
                            lamp_asset_id, lamp_asset_name = create_lamp.get("id"), create_lamp.get("name")
                            ilm_lamp_attributes.update(LAMP_INSTALLED_STATE_CONDITION)
                            ilm_lamp_attributes.update(device_service.construct_attributes(
                                request_attributes=lamp_details, valid_keys=LAMP_INSTALLATION_ATTRIBUTES))
                            light_point_attributes.update(
                                {LIGHT_POINT_INSTALLATION_ATTRIBUTES[9]: lamp_details.get("lampWatts")})
                        else:
                            lamp_asset_id, lamp_asset_name = get_lamp.get("id").get("id"), get_lamp.get("name")
                            to_relation_query = asset_service.query_lamp_pole_asset_relation_query(
                                entity_id=lamp_asset_id, entity_type=ENTITY_TYPE[1], direction=ENTITY_RELATIONS[1],
                                relation_type=RELATION_TYPES[5], max_level=2)
                            to_relation_detail = device_service.find_entities_by_query(query=to_relation_query)
                            if to_relation_detail["data"]:
                                from_info = asset_service.pole_details_construct(data=to_relation_detail["data"])
                                customer = customer_service.get_customer_by_id(
                                    owner_type=OWNER_TYPE[0], owner_id=get_lamp.get("ownerId").get("id"))
                                message = asset_service.construct_pubsub_lamp_install_report_data(
                                    lamp_data=from_info, lamp_det=lamp_det, pole_data=from_info, img_name=img_data,
                                    details=lamp_details, remove=True, customer_name=customer.get("name"))
                                # TODO validate this publish well
                                asset_service.publish_lamp_data_to_pubsub(message=message)
                            device_service.get_and_remove_from_and_to_relations(entity_id=lamp_asset_id,
                                                                                entity_type=ENTITY_TYPE[1])
                            ilm_lamp_attributes.update(INSTALLED_STATE)
                            if get_lamp.get("ownerId").get("id") != request_params.get("customerId"):
                                asset_service.change_owner(owner_type=OWNER_TYPE[0],
                                                           owner_id=request_params.get("customerId"),
                                                           entity_id=lamp_asset_id)
                    else:
                        construct_new_lamp_asset = asset_service.create_asset_entity(request_param=ILM_ASSET_TYPES[0],
                                                                                     data=request_params,
                                                                                     label=device_name)
                        ilm_lamp_attributes.update(LAMP_INSTALLED_STATE_CONDITION)
                        create_lamp = asset_service.create_and_update_asset_name(details=construct_new_lamp_asset)
                        lamp_asset_id, lamp_asset_name = create_lamp.get("id"), create_lamp.get("name")

                    ilm_lamp_attributes.update(device_service.construct_attributes(
                        request_attributes=request_params, valid_keys=LAMP_INSTALLATION_ATTRIBUTES[4:7]))
                    asset_service.create_entity_relation(
                        from_id=light_point_id, from_type=ENTITY_TYPE[1], to_id=lamp_asset_id, to_type=ENTITY_TYPE[1],
                        relation_type=RELATION_TYPES[2])
                    request_handler.update_service_response(asset_service.create_entity_relation(
                        from_id=ward_id, from_type=ENTITY_TYPE[1], to_id=light_point_id, to_type=ENTITY_TYPE[1],
                        relation_type=RELATION_TYPES[0]))
                    light_point_attributes.update(device_service.construct_attributes(
                        request_attributes=request_params, valid_keys=LIGHT_POINT_INSTALLATION_ATTRIBUTES))
                    light_point_attributes.update(INSTALLED_STATE)
                    ilm_attributes.update(device_service.construct_attributes(request_attributes=request_params,
                                                                              valid_keys=ILM_INSTALLATION_ATTRIBUTES))
                    ilm_attributes.update(INSTALLED_STATE)
                    ilm_add_attributes = device_service.create_attributes(
                        entity_type=device_details.get("type").lower(), lp=light_point_name, lamp=lamp_asset_name,
                        pole_name=pole_data.get("name") if pole_data else "")
                    ilm_attributes.update(ilm_add_attributes)
                    light_point_add_attributes = device_service.create_attributes(
                        entity_type=ILM_ASSET_TYPES[1], ilm=device_name, lamp=lamp_asset_name,
                        pole_name=pole_data.get("name") if pole_data else "")
                    light_point_attributes.update(light_point_add_attributes)
                    lamp_add_attributes = device_service.create_attributes(
                        entity_type=ILM_ASSET_TYPES[0], lp=light_point_name, ilm=device_name,
                        pole_name=pole_data.get("name") if pole_data else "")
                    ilm_lamp_attributes.update(lamp_add_attributes)
                    attr = {}
                    if pole_id:
                        asset_service.create_entity_relation(
                            from_id=pole_id, from_type=ENTITY_TYPE[1], to_id=light_point_id, to_type=ENTITY_TYPE[1],
                            relation_type=RELATION_TYPES[5])
                        pole_det = asset_service.asset_details_query_construct(
                            entity_id=pole_id, entity_type=ENTITY_TYPE[1])
                        pole_attr_const = device_service.find_entities_by_query(query=pole_det)
                        attr.update(asset_service.pole_details_construct(data=pole_attr_const["data"]))
                    request_params["pole"] = attr
                    message = asset_service.construct_pubsub_lamp_install_report_data(
                        lamp_data=request_params, lamp_det=lamp_asset_name, pole_data=attr, details=lamp_details,
                        img_name=img_data, customer_name=default_customer_name.get("name"))
                    # TODO validate this publish well
                    asset_service.publish_lamp_data_to_pubsub(message=message)
                    asset_service.create_entity_relation(
                        from_id=light_point_id, from_type=ENTITY_TYPE[1], to_id=lamp_asset_id, to_type=ENTITY_TYPE[1],
                        relation_type=RELATION_TYPES[2], additional_info={"lamp": message})
                    ilm_server_attributes = device_service.device_asset_server_attributes_construct(
                        entity_id=ilm_device_id, entity_type=ENTITY_TYPE[0], server_attributes=ilm_attributes)
                    light_point_server_attributes = device_service.device_asset_server_attributes_construct(
                        entity_id=light_point_id,
                        entity_type=ENTITY_TYPE[1],
                        server_attributes=light_point_attributes)
                    lamp_server_attributes = device_service.device_asset_server_attributes_construct(
                        entity_id=lamp_asset_id,
                        entity_type=ENTITY_TYPE[1],
                        server_attributes=ilm_lamp_attributes)
                    aws_server_attributes_api = {"ilm": ilm_server_attributes,
                                                 "lightPoint": light_point_server_attributes,
                                                 "lamp": lamp_server_attributes}
                    device_service.send_datas_to_sqs(data=aws_server_attributes_api)
                    device_service.update_entity_attribute(
                        entity_type=ENTITY_TYPE[0], entity_id=ilm_device_id, scope=ATTRIBUTE_SCOPES[1],
                        data=json.dumps(ilm_attributes))
                    device_service.update_entity_attribute(
                        entity_type=ENTITY_TYPE[1], entity_id=light_point_id, scope=ATTRIBUTE_SCOPES[1],
                        data=json.dumps(light_point_add_attributes))
                    device_service.update_entity_attribute(
                        entity_type=ENTITY_TYPE[1], entity_id=lamp_asset_id, scope=ATTRIBUTE_SCOPES[1],
                        data=json.dumps(ilm_lamp_attributes))
                    light_point_attributes.clear()
                    ilm_attributes.clear()
                    ilm_lamp_attributes.clear()
                    asset_service.update_telemetry(
                        entity_type=ENTITY_TYPE[0], entity_id=ilm_device_id, data=telemetry_data)
                    asset_service.update_telemetry(
                        entity_type=ENTITY_TYPE[1], entity_id=lamp_asset_id, data=telemetry_data)
                    asset_service.update_telemetry(
                        entity_type=ENTITY_TYPE[1], entity_id=light_point_id, data=telemetry_data)

                # Placing ILM under region group
                device_service.place_entity_under_group(
                    entity_ids=[ilm_device_id], owner_type=OWNER_TYPE_CUSTOMER, owner_id=customer_id,
                    group_type=ENTITY_TYPE_DEVICE, group_name=region)
                # Placing lightpoint, lamp and pole under region group
                asset_service.place_entity_under_group(
                    entity_ids=[light_point_id, lamp_asset_id], owner_type=OWNER_TYPE_CUSTOMER, owner_id=customer_id,
                    group_type=ENTITY_TYPE_ASSET, group_name=region)
                try:
                    if pole_id:
                        asset_service.place_entity_under_group(
                            entity_ids=[pole_id], owner_type=OWNER_TYPE_CUSTOMER, owner_id=customer_id,
                            group_type=ENTITY_TYPE_ASSET, group_name=region)
                except Exception as e:
                    logger.exception("Placing pole under region failure: ", e)
            else:
                request_handler.update_service_response({"status": 400, "message": "Invalid request body"})
        except Exception as e:
            logger.exception("Couldn't install ilm - %s" % str(e))
        return request_handler.get_service_response()

    @staticmethod
    def ilm_replace(request_params, asset_service, device_service, request_handler):
        try:
            request_params = request_params
            device_service = device_service
            asset_service = asset_service
            request_handler = request_handler
            get_old_device_details = request_params.get("remove")
            customer_id = request_params.get("customerId")
            region = request_params.get('region')
            ilm_device_id = get_old_device_details.get("ilmId")
            img_data = request_params.get('auditImg')
            telemetry_data = {"ts": int(time.time()) * 1000,
                              "values": {"auditImg": img_data, "pkt": 102, "activity": "Remove"}}

            if request_params.get("lightPointId"):
                light_point_id = request_params.get("lightPointId")
            else:
                light_point = device_service.get_entity_to_relation_info(to_id=ilm_device_id, to_type=ENTITY_TYPE[0])
                light_point_id = light_point[0]["from"]["id"]

            light_point_attr = {"ilm": "-", "state": "INSTALLED", "uid": "-"}
            old_relations = device_service.get_and_remove_from_and_to_relations(entity_id=ilm_device_id,
                                                                                entity_type=ENTITY_TYPE[0])

            device_service.update_entity_attribute(entity_type=ENTITY_TYPE[1], entity_id=light_point_id,
                                                   data=json.dumps(light_point_attr), scope=ATTRIBUTE_SCOPES[1])


            ilm_old_attributes = device_service.construct_attributes(
                request_attributes=REPLACE_AND_REMOVE_DEVICE_STATE_AND_CONDITION,
                valid_keys=ILM_INSTALLATION_ATTRIBUTES)
            device_service.update_entity_attribute(entity_type=ENTITY_TYPE[0],
                                                   entity_id=ilm_device_id, scope=ATTRIBUTE_SCOPES[1],
                                                   data=json.dumps(ilm_old_attributes))
            lp_to_relation = asset_service.find_by_to_relations(to_type=ENTITY_TYPE[1], to_id=light_point_id,
                                                                relation_type=RELATION_TYPES[3])
            if lp_to_relation:
                for relation in lp_to_relation:
                    asset_service.remove_entity_relation(from_id=relation["from"]["id"],
                                                         from_type=relation["from"]["entityType"],
                                                         to_type=relation["to"]["entityType"],
                                                         to_id=relation["to"]["id"], relation_type=relation["type"])
            asset_service.update_telemetry(entity_type=ENTITY_TYPE[0], entity_id=ilm_device_id, data=telemetry_data)
            # to remove old ilm (for replacement and removal activity)
            try:
                ilm_info = device_service.get_entity_info_by_id(entity_type=ENTITY_TYPE_DEVICE, entity_id=ilm_device_id)
                for group in ilm_info.get("groups", []):
                    rem_region = group.get("name")
                    rem_customer_id = ilm_info.get("ownerId", {}).get("id")
                    if rem_customer_id != customer_id:
                        device_service.remove_entity_under_group(
                            entity_ids=[ilm_device_id], owner_type=OWNER_TYPE_CUSTOMER,
                            owner_id=rem_customer_id, group_type=ENTITY_TYPE_DEVICE, group_name=rem_region)
                    else:
                        if rem_region != region:
                            device_service.remove_entity_under_group(
                                entity_ids=[ilm_device_id], owner_type=OWNER_TYPE_CUSTOMER,
                                owner_id=customer_id, group_type=ENTITY_TYPE_DEVICE, group_name=rem_region)
                        else:
                            device_service.remove_entity_under_group(
                                entity_ids=[ilm_device_id], owner_type=OWNER_TYPE_CUSTOMER,
                                owner_id=customer_id, group_type=ENTITY_TYPE_DEVICE, group_name=region)

            except Exception as e:
                logger.exception("removing the ilm under region group ", e)
            pole_data = {}
            if request_params.get("pole"):
                pole_data = request_params.get("pole")
            ilm_new_attributes = {}
            if request_params.get("ilm"):
                lp_asset_id = request_params.get("lightPointId")
                get_customer_info_from_lp = asset_service.get_entity_by_id(entity_type=ENTITY_TYPE[1],
                                                                           entity_id=lp_asset_id)
                get_new_device_details = request_params.get("ilm")
                new_ilm_name = get_new_device_details.get("name")
                new_device = device_service.get_entity_by_name(entity_type=ENTITY_TYPE[0], name=new_ilm_name)
                new_device_id = new_device.get("id").get("id")
                customer_id = request_params.get("customerId")
                try:
                    new_device_relations = device_service.get_and_remove_from_and_to_relations(entity_id=new_device_id, entity_type=ENTITY_TYPE[0])
                    new_light_point_id = new_device_relations[1][0].get('from').get('id')
                    new_light_point_attr = {"ilm": "-", "state": "INSTALLED", "uid": "-"}
                    device_service.update_entity_attribute(entity_type=ENTITY_TYPE[1], entity_id=new_light_point_id,
                                            data=json.dumps(new_light_point_attr), scope=ATTRIBUTE_SCOPES[1])
                except Exception as e:
                    logger.exception(f"Unexpected error occurred : {e.args}")

                device_service.create_from_and_to_relations(from_relation=old_relations[0],
                                                            to_relation=old_relations[1],
                                                            entity_id=new_device_id, entity_type=ENTITY_TYPE[0])
                get_customer_info_from_lp["label"] = new_ilm_name
                asset_service.create_asset(entity_type=ENTITY_TYPE[1], data=get_customer_info_from_lp)
                from_relation_query = device_service.device_relations(entity_id=lp_asset_id, entity_type=ENTITY_TYPE[1],
                                                                      direction=ENTITY_RELATIONS[0],
                                                                      relation_type=RELATION_TYPES[2])
                from_relation_detail = device_service.find_entities_by_query(query=from_relation_query)
                if "data" in from_relation_detail and from_relation_detail["data"]:
                    from_info = device_service.relation_construct(data=from_relation_detail["data"])
                    lamp_name = from_info["name"]
                    get_lamp_asset_details = asset_service.get_entity_by_name(entity_type=ENTITY_TYPE[1],
                                                                              name=lamp_name)
                    get_lamp_asset_details["label"] = new_ilm_name
                    asset_service.create_and_update_asset_name(details=get_lamp_asset_details)
                    ilm_new_attributes.update(device_service.construct_attributes(request_attributes=request_params,
                                                                                  valid_keys=ILM_INSTALLATION_ATTRIBUTES))
                    ilm_new_attributes.update(INSTALLED_STATE)
                    ilm_add_attributes = device_service.create_attributes(
                        entity_type=new_device.get("type").lower(), lp=get_customer_info_from_lp.get("name"),
                        lamp=lamp_name, pole_name=pole_data.get("name") if pole_data else "")
                    light_point_add_attributes = device_service.create_attributes(
                        entity_type=ILM_ASSET_TYPES[1], ilm=new_ilm_name, lamp=lamp_name,
                        pole_name=pole_data.get("name") if pole_data else "")
                    lamp_add_attributes = device_service.create_attributes(
                        ilm=new_ilm_name, entity_type=ILM_ASSET_TYPES[0], lp=get_customer_info_from_lp.get("name"),
                        pole_name=pole_data.get("name") if pole_data else "")
                    ilm_new_attributes.update(ilm_add_attributes)

                    device_service.update_entity_attribute(entity_type=ENTITY_TYPE[0],
                                                           entity_id=new_device_id, scope=ATTRIBUTE_SCOPES[1],
                                                           data=json.dumps(ilm_new_attributes))
                    device_service.update_entity_attribute(entity_type=ENTITY_TYPE[1],
                                                           entity_id=lp_asset_id, scope=ATTRIBUTE_SCOPES[1],
                                                           data=json.dumps(light_point_add_attributes))
                    device_service.update_entity_attribute(entity_type=ENTITY_TYPE[1],
                                                           entity_id=from_info["id"], scope=ATTRIBUTE_SCOPES[1],
                                                           data=json.dumps(lamp_add_attributes))
                    telemetry_data = {"ts": int(time.time()) * 1000,
                                      "values": {"auditImg": img_data, "pkt": 102, "activity": "Replace"}}
                    if customer_id != new_device.get("ownerId").get("id"):
                        device_service.change_owner(owner_type=OWNER_TYPE[0], owner_id=customer_id,
                                                    entity_id=new_device_id)
                    ilm_new_attributes.clear()
                    asset_service.update_telemetry(entity_type=ENTITY_TYPE[0], entity_id=new_device_id,
                                                   data=telemetry_data)
                    # to add new ilm (customer id,region for old and new ilm is same )
                    try:
                        # Placing ilm under region group
                        device_service.place_entity_under_group(
                            entity_ids=[new_device_id], owner_type=OWNER_TYPE_CUSTOMER,
                            owner_id=customer_id, group_type=ENTITY_TYPE_DEVICE, group_name=region)
                    except Exception as e:
                        logger.exception("placing the ilm under region group ", e)
        except Exception as e:
            request_handler.update_error_response(str(e))
            logger.exception("ilm couldn't replace %s" % str(e))
        return request_handler.get_service_response()

    @staticmethod
    def replace_ilm_and_lamp(request_params, device_service, asset_service, request_handler, lamp_service,
                             customer_service):
        try:
            logger.info("Replacing ILM with Lamp: %s" % request_params)
            old_device_details = request_params.get("remove")
            customer_id = request_params.get("customerId")
            region = request_params.get("region")
            ilm_device_id = old_device_details.get("ilmId")
            lamp_id = old_device_details.get("lampId")
            img_data = request_params.get("auditImg")
            lamp_attr = asset_service.get_entity_attribute(entity_type=ENTITY_TYPE[1], entity_id=lamp_id,
                                                           scope=ATTRIBUTE_SCOPES[1],
                                                           keys="manufacturer,year,lampType,lampWatts,dimmable")
            lamp_details = asset_service.get_entity_by_id(entity_type=ENTITY_TYPE[1], entity_id=lamp_id)
            customer_det = customer_service.get_customer_by_id(owner_type=OWNER_TYPE[0],
                                                               owner_id=lamp_details.get("ownerId").get("id"))
            telemetry_data = {"ts": int(time.time()) * 1000,
                              "values": {"auditImg": img_data, "pkt": 102, "activity": "Remove"}}
            remove_attr = {}
            remove_attr1 = {}
            if request_params.get("poleId"):
                pole_attr = asset_service.get_entity_attribute(entity_type=ENTITY_TYPE[1],
                                                               entity_id=request_params.get("poleId"),
                                                               scope=ATTRIBUTE_SCOPES[1],
                                                               keys="armCount,type,connection,lampProfiles")
                for attribute in pole_attr:
                    remove_attr1[attribute.get("key")] = attribute.get("value")
            else:
                pole_attr = asset_service.query_lamp_pole_asset_relation_query(entity_id=lamp_id,
                                                                               entity_type=ENTITY_TYPE[1],
                                                                               direction=ENTITY_RELATIONS[1],
                                                                               relation_type=RELATION_TYPES[5],
                                                                               max_level=2)
                pole_attr_const = device_service.find_entities_by_query(query=pole_attr)
                remove_attr1.update(asset_service.pole_details_construct(data=pole_attr_const["data"]))
            if request_params.get("lightPointId"):
                light_point_id = request_params.get("lightPointId")
            else:
                light_point = device_service.get_entity_to_relation_info(to_id=ilm_device_id, to_type=ENTITY_TYPE[0])
                light_point_id = light_point[0]["from"]["id"]

            light_point_attr = {"ilm": "-", "lamp": "-", "state": "INSTALLED", "uid": "-"}
            lp_attr = asset_service.get_entity_attribute(entity_type=ENTITY_TYPE[1],
                                                         entity_id=light_point_id, scope=ATTRIBUTE_SCOPES[1],
                                                         keys="installedOn,installedBy,latitude,longitude,"
                                                              "landmark,zoneName,wardName,region")
            for attr in lp_attr:
                remove_attr[attr.get("key")] = attr.get("value")
            old_device_details.update(remove_attr)
            message = asset_service.construct_pubsub_lamp_install_report_data(
                lamp_data=old_device_details, lamp_det=lamp_details.get("name"), details=lamp_attr,
                pole_data=remove_attr1, remove=True, customer_name=customer_det["name"], img_name=img_data)
            old_ilm_relations = device_service.get_and_remove_from_and_to_relations(entity_id=ilm_device_id,
                                                                                    entity_type=ENTITY_TYPE[0])
            device_service.get_and_remove_from_and_to_relations(entity_id=lamp_id, entity_type=ENTITY_TYPE[1])
            old_lamp_attributes = device_service.construct_attributes(
                request_attributes=REPLACE_AND_REMOVE_DEVICE_STATE_AND_CONDITION,
                valid_keys=LAMP_INSTALLATION_ATTRIBUTES)
            old_ilm_attributes = device_service.construct_attributes(
                request_attributes=REPLACE_AND_REMOVE_DEVICE_STATE_AND_CONDITION,
                valid_keys=ILM_INSTALLATION_ATTRIBUTES)
            device_service.update_entity_attribute(entity_type=ENTITY_TYPE[1], entity_id=lamp_id,
                                                   scope=ATTRIBUTE_SCOPES[1], data=json.dumps(old_lamp_attributes))
            device_service.update_entity_attribute(entity_type=ENTITY_TYPE[0],
                                                   entity_id=ilm_device_id, scope=ATTRIBUTE_SCOPES[1],
                                                   data=json.dumps(old_ilm_attributes))
            device_service.update_entity_attribute(entity_type=ENTITY_TYPE[1], entity_id=light_point_id,
                                                   data=json.dumps(light_point_attr), scope=ATTRIBUTE_SCOPES[1])
            lp_to_relation = asset_service.find_by_to_relations(to_type=ENTITY_TYPE[1], to_id=light_point_id,
                                                                relation_type=RELATION_TYPES[3])
            if lp_to_relation:
                for relation in lp_to_relation:
                    asset_service.remove_entity_relation(from_id=relation["from"]["id"],
                                                         from_type=relation["from"]["entityType"],
                                                         to_type=relation["to"]["entityType"],
                                                         to_id=relation["to"]["id"], relation_type=relation["type"])
            asset_service.publish_lamp_data_to_pubsub(message=message)
            asset_service.update_telemetry(entity_type=ENTITY_TYPE[0], entity_id=ilm_device_id, data=telemetry_data)
            asset_service.update_telemetry(entity_type=ENTITY_TYPE[1], entity_id=lamp_id, data=telemetry_data)


            # to remove old ilm and lamp(customer id,region for old and new ilm ,lamp is same )
            try:
                ilm_info = device_service.get_entity_info_by_id(entity_type=ENTITY_TYPE_DEVICE, entity_id=ilm_device_id)
                for group in ilm_info.get("groups", []):
                    rem_region = group.get("name")
                    rem_customer_id = ilm_info.get("ownerId", {}).get("id")
                    if rem_customer_id != customer_id:
                        device_service.remove_entity_under_group(
                            entity_ids=[ilm_device_id], owner_type=OWNER_TYPE_CUSTOMER,
                            owner_id=rem_customer_id, group_type=ENTITY_TYPE_DEVICE, group_name=rem_region)
                        asset_service.remove_entity_under_group(
                            entity_ids=[lamp_id], owner_type=OWNER_TYPE_CUSTOMER,
                            owner_id=rem_customer_id, group_type=ENTITY_TYPE_ASSET, group_name=rem_region)
                    else:
                        if rem_region != region:
                            device_service.remove_entity_under_group(
                                entity_ids=[ilm_device_id], owner_type=OWNER_TYPE_CUSTOMER,
                                owner_id=customer_id, group_type=ENTITY_TYPE_DEVICE, group_name=rem_region)
                            asset_service.remove_entity_under_group(
                                entity_ids=[lamp_id], owner_type=OWNER_TYPE_CUSTOMER,
                                owner_id=customer_id, group_type=ENTITY_TYPE_ASSET, group_name=rem_region)
                        else:
                            device_service.remove_entity_under_group(
                                entity_ids=[ilm_device_id], owner_type=OWNER_TYPE_CUSTOMER,
                                owner_id=customer_id, group_type=ENTITY_TYPE_DEVICE, group_name=region)
                            asset_service.remove_entity_under_group(
                                entity_ids=[lamp_id], owner_type=OWNER_TYPE_CUSTOMER,
                                owner_id=customer_id, group_type=ENTITY_TYPE_ASSET, group_name=region)

            except Exception as e:
                logger.exception("removing the ilm , lamp under region group ", e)

            new_ilm_attributes = {}
            replace_lamp_attributes = {}
            pole_data = {}
            if request_params.get("pole"):
                pole_data = request_params.get("pole")
            if request_params.get("ilm") and request_params.get("lamp") or request_params.get("lightPointId"):
    
                lp_asset_id = request_params.get("lightPointId")
                get_customer_info_from_lp = asset_service.get_entity_by_id(entity_type=ENTITY_TYPE[1],
                                                                           entity_id=lp_asset_id)
                get_new_device_details = request_params.get("ilm")
                new_ilm_name = get_new_device_details.get("name")
                new_device = device_service.get_entity_by_name(entity_type=ENTITY_TYPE[0], name=new_ilm_name)
                new_device_id = new_device.get("id").get("id")
                customer_id = request_params.get("customerId")

                try:
                    new_device_relations = device_service.get_and_remove_from_and_to_relations(entity_id=new_device_id, entity_type=ENTITY_TYPE[0])
                    new_light_point_id = new_device_relations[1][0].get('from').get('id')
                    new_light_point_attr = {"ilm": "-", "state": "INSTALLED", "uid": "-"}
                    device_service.update_entity_attribute(entity_type=ENTITY_TYPE[1], entity_id=new_light_point_id,
                                            data=json.dumps(new_light_point_attr), scope=ATTRIBUTE_SCOPES[1])
                except Exception as e:
                    logger.exception(f"Unexpected error occurred : {e.args}")

                device_service.get_and_remove_from_and_to_relations(entity_id=new_device_id, entity_type=ENTITY_TYPE[0])
                device_service.create_from_and_to_relations(from_relation=old_ilm_relations[0],
                                                            to_relation=old_ilm_relations[1],
                                                            entity_id=new_device_id, entity_type=ENTITY_TYPE[0])
                get_customer_info_from_lp["label"] = new_ilm_name
                asset_service.create_asset(entity_type=ENTITY_TYPE[1], data=get_customer_info_from_lp)
                new_lamp_details = request_params.get("lamp")
                if new_lamp_details:
                    lamp_det = "{manufacturer}-{wattage}-{type}-{dimmable}-{year}-{name}".format(
                        manufacturer=str(new_lamp_details.get("manufacturer")),
                        wattage=str(new_lamp_details.get("lampWatts")).zfill(4),
                        type=str(new_lamp_details.get("lampType")),
                        dimmable=str(new_lamp_details.get("dimmable")), year=str(new_lamp_details.get("year")),
                        name=str(new_lamp_details.get("name")))
                    get_lamp = asset_service.get_entity_by_name(entity_type=ENTITY_TYPE[1], name=lamp_det)


                    if get_lamp.get("status") == 400 or get_lamp.get("status") == 404:
                        construct_new_lamp_asset = asset_service.create_lamp_asset(data=request_params, detail=lamp_det)
                        replace_lamp_attributes.update(LAMP_INSTALLED_STATE_CONDITION)
                        replace_lamp_attributes.update(device_service.construct_attributes(
                            request_attributes=new_lamp_details, valid_keys=LAMP_INSTALLATION_ATTRIBUTES))
                        create_lamp = asset_service.create_and_update_asset_name(details=construct_new_lamp_asset)
                        lamp_asset_id, lamp_asset_name = create_lamp.get("id"), create_lamp.get("name")
                    else:
                        lamp_asset_id, lamp_asset_name = get_lamp.get("id").get("id"), get_lamp.get("name")
                        to_relation_query = asset_service.query_lamp_pole_asset_relation_query(
                            entity_id=lamp_asset_id, entity_type=ENTITY_TYPE[1], direction=ENTITY_RELATIONS[1],
                            relation_type=RELATION_TYPES[5], max_level=2)

                        to_relation_detail = device_service.find_entities_by_query(query=to_relation_query)
                        if "data" in to_relation_detail and to_relation_detail["data"]:
                            from_info = asset_service.pole_details_construct(data=to_relation_detail["data"])
                            customer = customer_service.get_customer_by_id(owner_type=OWNER_TYPE[0],
                                                                           owner_id=get_lamp.get("ownerId").get("id"))
                            message = asset_service.construct_pubsub_lamp_install_report_data(
                                lamp_data=from_info, details=new_lamp_details, lamp_det=lamp_asset_name,
                                pole_data=from_info, remove=True, customer_name=customer_det["name"], img_name=img_data)
                            try:
                                asset_service.publish_lamp_data_to_pubsub(message=message,
                                                                          topic_path=PUBSUB_LAMP_TOPIC_PATH)
                            except Exception as e:
                                logger.exception("Error publishing lamp data to pubsub: %s", e)

                        device_service.get_and_remove_from_and_to_relations(entity_id=lamp_asset_id,
                                                                            entity_type=ENTITY_TYPE[1])
                        replace_lamp_attributes.update(INSTALLED_STATE)
                        if get_lamp.get("ownerId").get("id") != request_params.get("customerId"):
                            asset_service.change_owner(owner_type=OWNER_TYPE[0],
                                                       owner_id=request_params.get("customerId"),
                                                       entity_id=lamp_asset_id)
                else:
                    construct_new_lamp_asset = lamp_service.construct_lamp_entity(data=request_params,
                                                                                  label=new_ilm_name)
                    replace_lamp_attributes.update(LAMP_INSTALLED_STATE_CONDITION)
                    create_lamp = asset_service.create_and_update_asset_name(details=construct_new_lamp_asset)
                    lamp_asset_id, lamp_asset_name = create_lamp.get("id"), create_lamp.get("name")
                replace_lamp_attributes.update(device_service.construct_attributes(
                    request_attributes=request_params, valid_keys=ILM_INSTALLATION_ATTRIBUTES[7:]))
                new_ilm_attributes.update(device_service.construct_attributes(
                    request_attributes=request_params, valid_keys=ILM_INSTALLATION_ATTRIBUTES))
                new_ilm_attributes.update(INSTALLED_STATE)
                ilm_add_attributes = device_service.create_attributes(
                    entity_type=new_device.get("type").lower(), lp=get_customer_info_from_lp.get("name"),
                    lamp=lamp_asset_name, pole_name=pole_data.get("name") if pole_data else "")
                light_point_add_attributes = device_service.create_attributes(
                    entity_type=ILM_ASSET_TYPES[1], ilm=new_ilm_name, lamp=lamp_asset_name,
                    pole_name=pole_data.get("name") if pole_data else "")

                if new_lamp_details and new_lamp_details.get("lampWatts"):
                    light_point_add_attributes.update(
                        {LIGHT_POINT_INSTALLATION_ATTRIBUTES[9]: new_lamp_details.get("lampWatts")})

                lamp_add_attributes = device_service.create_attributes(
                    ilm=new_ilm_name, entity_type=ILM_ASSET_TYPES[0], lp=get_customer_info_from_lp.get("name"),
                    pole_name=pole_data.get("name") if pole_data else "")
                replace_lamp_attributes.update(lamp_add_attributes)
                new_ilm_attributes.update(ilm_add_attributes)
                device_service.update_entity_attribute(entity_type=ENTITY_TYPE[0],
                                                       entity_id=new_device_id, scope=ATTRIBUTE_SCOPES[1],
                                                       data=json.dumps(new_ilm_attributes))
                device_service.update_entity_attribute(entity_type=ENTITY_TYPE[1],
                                                       entity_id=lp_asset_id, scope=ATTRIBUTE_SCOPES[1],
                                                       data=json.dumps(light_point_add_attributes))
                device_service.update_entity_attribute(entity_type=ENTITY_TYPE[1],
                                                       entity_id=lamp_asset_id, scope=ATTRIBUTE_SCOPES[1],
                                                       data=json.dumps(replace_lamp_attributes))

                attr = {}
                if request_params.get("poleId"):
                    pole_det = asset_service.asset_details_query_construct(entity_id=request_params.get("poleId"),
                                                                           entity_type=ENTITY_TYPE[1])
                    pole_attr_const = device_service.find_entities_by_query(query=pole_det)
                    attr.update(asset_service.pole_details_construct(data=pole_attr_const["data"]))
                message = asset_service.construct_pubsub_lamp_install_report_data(
                    lamp_data=request_params, lamp_det=lamp_asset_name, pole_data=attr, details=new_lamp_details,
                    img_name=img_data, customer_name=customer_det["name"])

                try:
                    asset_service.publish_lamp_data_to_pubsub(message=message)
                except Exception as e:
                    logger.exception("Error publishing lamp data to pubsub: %s", e)

                asset_service.create_entity_relation(from_id=lp_asset_id, from_type=ENTITY_TYPE[1],
                                                     to_id=lamp_asset_id, to_type=ENTITY_TYPE[1],
                                                     relation_type=RELATION_TYPES[2], additional_info={"lamp": message})
                telemetry_data = {"ts": request_params.get("installedOn"),
                                  "values": {
                                      "auditImg": img_data, 
                                      "pkt": 102, 
                                      "activity": "Install"
                                      }
                                    }
                asset_service.update_telemetry(entity_type=ENTITY_TYPE[0], entity_id=new_device_id, data=telemetry_data)
                asset_service.update_telemetry(entity_type=ENTITY_TYPE[1], entity_id=lamp_asset_id, data=telemetry_data)
                asset_service.update_telemetry(entity_type=ENTITY_TYPE[1], entity_id=lp_asset_id, data=telemetry_data)
                if customer_id != new_device.get("ownerId").get("id"):
                    device_service.change_owner(owner_type=OWNER_TYPE[0], owner_id=customer_id, entity_id=new_device_id)
                # to add new ilm,lamp (customer id,region for old and new ilm,lamp is same )
                try:
                    # Placing ilm ,lamp under region group
                    device_service.place_entity_under_group(
                        entity_ids=[new_device_id], owner_type=OWNER_TYPE_CUSTOMER,
                        owner_id=customer_id, group_type=ENTITY_TYPE_DEVICE, group_name=region)
                    asset_service.place_entity_under_group(
                        entity_ids=[lamp_asset_id], owner_type=OWNER_TYPE_CUSTOMER,
                        owner_id=customer_id, group_type=ENTITY_TYPE_ASSET, group_name=region)
                except Exception as e:
                    logger.exception("placing the ilm, lamp under region group ", e)
                replace_lamp_attributes.clear()
                new_ilm_attributes.clear()
        except Exception as e:
            request_handler.update_error_response(str(e))
            logger.exception("ilm couldn't replace %s" % str(e))
        return request_handler.get_service_response()


class AssetService(EntityService):
    def __init__(self, token: str, instance: RequestResponseHandler, 
                 tenent_token:str = ''):
        self.tenent_header = {'content-type': 'application/json', 'X-Authorization': 'Bearer ' + tenent_token}

        super().__init__(token, instance)

    def save_token(self, token: str) -> None:
        self.header['X-Authorization'] = token

    def get_entity_by_name(self, entity_type: str, name: str) -> dict or None:
        url = BASE_URL + self.entity_by_name_url.format(entity_type=entity_type.lower(),
                                                        query_name="assetName", entity_name=quote(name))
        response = RequestResponseHandler.get_request(url=url, header=self.header, data='', instance=self.instance)

        if response.get('status') == 403:
            response = RequestResponseHandler.get_request(url=url, header=self.tenent_header, data='', instance=self.instance)
        return response

    def get_entity_by_id(self, entity_type: str, entity_id: str) -> dict or None:
        url = BASE_URL + self.entity_by_id_url.format(entity_type=entity_type.lower(), entity_id=entity_id)
        return RequestResponseHandler.get_request(url=url, header=self.header, data='', instance=self.instance)

    def get_entity_attribute(self, entity_type: str, entity_id: str, scope: str, keys: str) -> list or None:
        url = BASE_URL + self.entity_attribute_url.format(entity_type=entity_type,
                                                          entity_id=entity_id, scope=scope, keys=keys)
        return RequestResponseHandler.get_request(url=url, header=self.header, data='', instance=self.instance)

    def find_entities_by_query(self, query: dict) -> dict:
        url = BASE_URL + self.entity_query_find_url
        return RequestResponseHandler.post_request(url=url, header=self.header, data=json.dumps(query),
                                                   instance=self.instance)

    def update_telemetry(self, entity_type: str, entity_id: str, data: dict):
        url = BASE_URL + self.update_telemetry_url.format(entity_type=entity_type, entity_id=entity_id, scope="ANY")
        return RequestResponseHandler.post_request(url=url, header=self.header, data=json.dumps(data),
                                                   instance=self.instance)

    def update_entity_attribute(self, entity_type: str, entity_id: str, scope: str, data: str) -> list or None:
        url = BASE_URL + self.update_entity_attribute_url.format(entity_type=entity_type, entity_id=entity_id,
                                                                 scope=scope)
        return RequestResponseHandler.post_request(url=url, header=self.header, data=data, instance=self.instance)

    def remove_entity_relation(self, from_id: str, from_type: str, to_id: str, to_type: str,
                               relation_type: str) -> dict:
        url = BASE_URL + self.remove_relation.format(from_id=from_id, from_type=from_type, to_id=to_id,
                                                     to_type=to_type, relation_type=relation_type)
        return RequestResponseHandler.delete_request(url=url, header=self.header, data='', instance=self.instance)

    def create_asset(self, entity_type: str, data: dict) -> dict:
        url = BASE_URL + self.create_or_update_entity_url.format(entity_type=entity_type.lower())
        print("url: ", url)
        return RequestResponseHandler.post_request(url=url, header=self.header, data=json.dumps(data),
                                                   instance=self.instance)

    def create_or_update_entity(self, entity_type: str, data: dict) -> dict:
        validate_asset = self.get_entity_by_name(entity_type=entity_type, name=data.get('name'))
        if validate_asset.get("status") == 404:
            return self.create_asset(entity_type=entity_type, data=data)
        return {"status": 1000, "message": "CCMS/Gw already present", "details": validate_asset}

    def get_all_regions(self, entity_type: str, pagesize: int, page: int, asset_type: str) -> dict:
        url = BASE_URL + self.get_all_regions_url.format(entity_type=entity_type.lower(), pagesize=pagesize,
                                                         page=page,
                                                         asset_type=asset_type)
        return RequestResponseHandler.get_request(url=url, header=self.header, data='', instance=self.instance)

    def get_asset_entities(self, from_id: str, from_type: str) -> dict:
        url = BASE_URL + self.get_all_entities_url.format(from_id=from_id, from_type=from_type)
        return RequestResponseHandler.get_request(url=url, header=self.header, data='', instance=self.instance)

    def asset_related_asset(self, query: dict) -> dict:
        url = BASE_URL + self.entity_query_find_url.format()
        return RequestResponseHandler.post_request(url=url, header=self.header, data=json.dumps(query),
                                                   instance=self.instance)

    def get_entity_from_relation(self, from_id: str, from_type: str) -> list:
        url = BASE_URL + self.get_from_relation_info_url.format(from_id=from_id, from_type=from_type)
        return RequestResponseHandler.get_request(url=url, header=self.header, data='', instance=self.instance)

    def remove_asset_relations(self, asset_relations: list, related_id: str, related_type: str,
                               removal_type: str) -> None:
        for relation in asset_relations:
            self.remove_entity_relation(
                from_id=related_id, from_type=related_type,
                to_id=relation.get(removal_type).get("id"), to_type=relation.get(removal_type).get("entityType"),
                relation_type=RELATION_TYPES[1])

    def change_owner(self, owner_type: str, owner_id: str, entity_id: str, entity_type: str = ENTITY_TYPE[1]):
        url = BASE_URL + self.change_ownership_url.format(owner_type=owner_type, owner_id=owner_id,
                                                          entity_id=entity_id, entity_type=entity_type)
        return RequestResponseHandler.post_request(url=url, header=self.header, data="", instance=self.instance)

    def validate_and_update_ccms(self, asset_detail: dict, device_name: str) -> dict:
        asset = {"name": "", "id": ""}
        if "details" in asset_detail:
            asset = {"name": asset_detail.get("details").get("name"),
                     "id": asset_detail.get("details").get("id").get("id")}
            asset_detail["details"]["label"] = device_name
            self.create_asset(entity_type=ENTITY_TYPE[1], data=asset_detail.get("details"))
            relations = self.get_entity_from_relation(from_id=asset.get("id"), from_type=ENTITY_TYPE[1])
            self.remove_asset_relations(asset_relations=relations, related_id=asset.get("id"),
                                        related_type=ENTITY_TYPE[1], removal_type=ENTITY_RELATIONS[1].lower())
        else:
            asset = {"name": asset_detail.get("name"), "id": asset_detail.get("id").get("id")}
        return asset

    def create_and_update_asset_name(self, details: dict, asset_type = None) -> dict:
        if asset_type in ILM_ASSET_TYPES[5:]:
            asset_detail = self.get_or_create_entity(entity_type=ENTITY_TYPE[1], data=details)
        else:
            asset_detail = self.create_or_update_entity(entity_type=ENTITY_TYPE[1], data=details)
        update_asset_detail = asset_detail.get('details') if asset_detail.get('details') else asset_detail
        return {"name": update_asset_detail.get("name"), "id": update_asset_detail.get("id").get("id"),
                "ownerId": update_asset_detail.get("ownerId")}

    def get_or_create_entity(self, entity_type: str, data: dict) -> dict:
        pole_details = self.create_asset(entity_type=entity_type, data=data)
        if pole_details.get("status") == 400:
            return self.get_entity_by_name(entity_type=entity_type, name=data.get('name'))
        return {"status": 1000, "message": "CCMS/Gw already present", "details": pole_details}

    def get_entity_group_id(self, owner_type: str, owner_id: str, group_type: str) -> dict:
        url = BASE_URL + self.get_entity_groupId_url.format(owner_type=owner_type, owner_id=owner_id,
                                                            group_type=group_type)
        return RequestResponseHandler.get_request(url=url, header=self.header, data="", instance=self.instance)

    def construct_region_details_asset(self, data: dict, tenant_admin: bool, customer_id: str = None) -> dict:
        region_details = []
        for details in data:
            if details.get("latest").get("ENTITY_FIELD").get("type").get("value") == ILM_ASSET_TYPES[2]:
                asset_detail = self.get_entity_by_name(entity_type=ENTITY_TYPE[1],
                                                        name=details.get("latest").get("ENTITY_FIELD").get(
                                                            "name").get("value"))
                region_details.append({"name": asset_detail.get("name"),
                                    "id": asset_detail.get("id").get("id"),
                                    "customerId": asset_detail.get("ownerId").get("id"),
                                        "poleSchema": details.get("latest", {}).get("SERVER_ATTRIBUTE", {})\
                                                        .get("poleSchema",{}).get("value", None)
                                    })
        return {"regions": region_details}

    def find_by_to_relations(self, to_type: str, to_id: str, relation_type: str):
        url = BASE_URL + self.get_entity_to_relation_url.format(entity_type=to_type, entity_id=to_id,
                                                                relation_type=relation_type)
        return RequestResponseHandler.get_request(url=url, header=self.header, data="", instance=self.instance)

    @staticmethod
    def construct_customer_details(data: dict, tenant_admin: bool) -> dict:
        cus_details = []
        if tenant_admin:
            for details in data:
                if details.get("id").get("entityType") == OWNER_TYPE[0]:
                    cus_details.append({"name": details.get("name"),
                                        "id": details.get("id").get("id")})
        else:
            for details in data:
                if details.get("latest").get("ENTITY_FIELD").get("type").get("value") == OWNER_TYPE[0]:
                    cus_details.append({"name": details.get("latest").get("ENTITY_FIELD").get("name").get("value"),
                                        "id": details.get("entityId").get("id")})
        return {"customers": cus_details}

    @staticmethod
    def construct_asset_image_link(asset_name: str):
        return "https://" + AWS_BUCKET_NAME + "." + AWS_BUCKET_TYPE + "." + AWS_BUCKET_REGION + "." + \
            "amazonaws.com" + "/" + AWS_BUCKET_LUMINATOR_FOLDER_NAME + "/" + asset_name

    @staticmethod
    def publish_pole_data_to_pubsub(pole_data: dict, customer_details: dict):
        """
        The function initializes the Google Cloud Pub/Sub client with a service account key,
        updates the pole_data dictionary with customer and Photo attributes, and publishes 
        the message to the specified Google Cloud Pub/Sub topic. The published message includes
        the pole_data as JSON data.
        Args:
            pole_data (dict): A dictionary containing pole data to be published.
            customer_details (dict): A dictionary containing customer information.
        Returns:
            None
        """
        try:
            # Initialize the Pub/Sub client with the service account key
            publisher = pubsub_v1.PublisherClient.from_service_account_json(SERVICE_ACCOUNT_KEY_PATH)

            if isinstance(pole_data.get("auditImg"), str):
                pole_data.update({
                    "customerName": customer_details["name"],
                    "Photo": AssetService.construct_asset_image_link(asset_name=pole_data["auditImg"])
                })
            elif isinstance(pole_data.get("auditImg"), dict):
                pole_data.update({
                    "customerName": customer_details["name"],
                    "Photo": [
                                AssetService.construct_asset_image_link(asset_name=value)
                                for value in pole_data["auditImg"].values()
                            ]
                })
            elif isinstance(pole_data.get("auditImg"), list):
                pole_data.update({
                    "customerName": customer_details["name"],
                    "Photo": [
                                AssetService.construct_asset_image_link(asset_name=image)
                                for image in pole_data["auditImg"]
                            ]
                })

            # Publish the message to the specified topic
            message_future = publisher.publish(PUBSUB_POLE_TOPIC_PATH, data=json.dumps(pole_data).encode('utf-8'))
            logger.info(f"Published message {message_future.result()} to {PUBSUB_POLE_TOPIC_PATH}")
        except Exception as e:
            logger.exception(f"Error publishing data to Pub/Sub: {str(e)}")

    @staticmethod
    def construct_pubsub_lamp_install_report_data(lamp_data: dict, lamp_det: str, pole_data: dict = None,
                                                  details: dict = None, remove: bool = None, customer_name: str = None,
                                                  img_name: str = None):
        """
        The function initializes the Google Cloud Pub/Sub client with a service account key,
        updates the lamp_data dictionary with customer ,pole details and Photo attributes, and publishes
        the message to the specified Google Cloud Pub/Sub topic. The published message includes
        the lamp_data as JSON data.
        Args:
            lamp_data (dict): A dictionary containing pole data to be published.
            customer_name(str):
            pole_data (dict): A dictionary containing customer information.
            lamp_det (str): Constructed Lamp name.
            details(dict).
            img_name(str).
            remove(bool)
        Returns:
            None
        """
        try:
            logger.info("Preparing message for pushing to BigQuery")
            lamp_type = {
                "01": "SL LED",
                "02": "FL LED"
            }
            manufacturer_map = {
                "SI": "Signify",
                "HA": "Havells",
                "OR": "Orient",
                "BJ": "Bajaj",
                "HI": "Hitro",
                "IN": "Inventa"
            }
            lamp_dim = {
                "00": False,
                "03": False,
                "15": True,
                "17": True
            }
            if remove:
                if details:
                    det = {}
                    if type(details) == list:
                        for data in details:
                            det[data.get("key")] = data.get("value")
                        installed_lamp = {"lampId": lamp_det, "type": lamp_type[str(det.get("lampType")).zfill(2)],
                                          "watts": int(det.get("lampWatts")),
                                          "manufacturer": manufacturer_map[det.get("manufacturer")]
                                          if det.get("manufacturer") in manufacturer_map else
                                          det.get("manufacturer"),
                                          "batchYear": int(det.get("year"))}
                        pin_type = str(det.get("dimmable")).zfill(2)
                        pin_dim = lamp_dim[pin_type]
                    else:
                        installed_lamp = {"lampId": lamp_det, "type": lamp_type[details.get("lampType")],
                                          "watts": int(details.get("lampWatts")),
                                          "manufacturer": manufacturer_map[details.get("manufacturer")]
                                          if details.get("manufacturer") in manufacturer_map else
                                          details.get("manufacturer"),
                                          "batchYear": int(details.get("year"))}
                        pin_type = str(details.get("dimmable")).zfill(2)
                        pin_dim = lamp_dim[pin_type]
                else:
                    installed_lamp = {"lampId": lamp_det, "type": "", "watts": 0, "manufacturer": "", "batchYear": 0}
                    pin_type = "-"
                    pin_dim = None
                replace_lamp = ""
                pole_profile = pole_data.get("lampProfiles") if pole_data else ""
                if pole_data:
                    if pole_data.get("type"):
                        pole_type = pole_data.get("type")
                    else:
                        pole_type = pole_data.get("pole_type")
                else:
                    pole_type = ""
                if pole_profile:
                    if type(pole_profile) == str:
                        lamp_profiles = json.loads(pole_profile)
                    else:
                        lamp_profiles = pole_profile
                    for profile in lamp_profiles:
                        replace_lamp = replace_lamp + str(profile.get("type")) + " - " + str(
                            profile.get("watts")) + ", "
                message = {"poleId": pole_data.get("name") if pole_data else "",
                           "region": lamp_data.get("region"),
                           "zone": lamp_data.get('zoneName'),
                           "ward": lamp_data.get('wardName'),
                           "location": lamp_data.get('landmark') if lamp_data.get("landmark") else lamp_data.get(
                               "location"),
                           "latitude": lamp_data.get('latitude'),
                           "longitude": lamp_data.get('longitude'),
                           "poleType": pole_type,
                           "poleArmCount": pole_data["armCount"] if pole_data else 0,
                           "installedBy": lamp_data['installedBy'],
                           "installedOn": int(time.time()),
                           "connectionType": pole_data.get("connection") if pole_data else "",
                           "installedLamp": installed_lamp,
                           "replacedLamps": replace_lamp.strip(),
                           "photoProof": AssetService.construct_asset_image_link(asset_name=img_name),
                           "activity": "Removal",
                           "customerName": customer_name,
                           "pinType": pin_type,
                           "dimmable": pin_dim
                           }
            else:
                if details:
                    installed_lamp = {"lampId": lamp_det, "type": lamp_type[details.get("lampType")],
                                      "watts": int(details.get("lampWatts")),
                                      "manufacturer": manufacturer_map[details.get("manufacturer")]
                                      if details.get("manufacturer") in manufacturer_map else
                                      details.get("manufacturer"),
                                      "batchYear": int(details.get("year"))}
                    pin_type = str(details.get("dimmable")).zfill(2)
                    pin_dim = lamp_dim[pin_type]
                else:
                    installed_lamp = {"lampId": lamp_det, "type": "", "watts": 0, "manufacturer": "", "batchYear": 0}
                    pin_type = "-"
                    pin_dim = None
                replace_lamp = ""
                pole_profile = pole_data.get("lampProfiles") if pole_data else ""
                if pole_data:
                    if pole_data.get("type"):
                        pole_type = pole_data.get("type")
                    else:
                        pole_type = pole_data.get("pole_type")
                else:
                    pole_type = ""
                if pole_profile:
                    if type(pole_profile) == str:
                        lamp_profiles = json.loads(pole_profile)
                    else:
                        lamp_profiles = pole_profile
                    for profile in lamp_profiles:
                        replace_lamp = replace_lamp + str(profile.get("type")) + " - " + str(
                            profile.get("watts")) + ", "

                installed_on = lamp_data.get("installedOn")
                installed_on_ts = int(installed_on) / 1000 if installed_on is not None else None

                message = {"poleId": pole_data.get("name") if pole_data else "",
                           "region": lamp_data.get("region"),
                           "zone": lamp_data.get("zoneName"),
                           "ward": lamp_data.get("wardName"),
                           "location": lamp_data.get("landmark"),
                           "latitude": lamp_data.get("latitude"),
                           "longitude": lamp_data.get("longitude"),
                           "poleType": pole_type,
                           "poleArmCount": pole_data.get("armCount") if pole_data else 0,
                           "installedBy": lamp_data.get("installedBy"),
                           "installedOn": installed_on_ts,
                           "connectionType": pole_data.get("connection") if pole_data else "",
                           "installedLamp": installed_lamp,
                           "replacedLamps": replace_lamp.strip(),
                           "photoProof": AssetService.construct_asset_image_link(asset_name=img_name),
                           "activity": "Installation",
                           "customerName": customer_name,
                           "pinType": pin_type,
                           "dimmable": pin_dim
                           }
            return message
        except Exception as e:
            logger.exception(f"Error publishing data to Pub/Sub: {e}")
            return None

    @staticmethod
    def publish_location_data_to_pubsub(message):
        publisher = pubsub_v1.PublisherClient.from_service_account_json(SERVICE_ACCOUNT_KEY_PATH)
        # Publish the message to the specified topic
        message_future = publisher.publish(PUBSUB_POLE_RELOCATION_TOPIC_PATH, data=json.dumps(message).encode('utf-8'))
        logger.info(f"Published message {message_future.result()} to {PUBSUB_POLE_RELOCATION_TOPIC_PATH}")

    @staticmethod
    def construct_pubsub_ebmeter_acitivty_log(request_params, activity, ebmeterNo):
        try:
            message = {
                "id": ebmeterNo,
                "customer": request_params.get("replaceWith").get("customer"),
                "region": request_params.get("replaceWith").get("region"),
                "zone": request_params.get("replaceWith").get("zoneName"),
                "ward": request_params.get("replaceWith").get("wardName"),
                "location": request_params.get("replaceWith").get("location"),
                "latitude": request_params.get("replaceWith").get("slatitude"),
                "longitude": request_params.get("replaceWith").get("slongitude"),
                "activity": activity,
                "activityBy": request_params.get("replaceWith").get("installedBy"),
                "actedOn": datetime.fromtimestamp(
                    int(request_params.get("replaceWith").get("installedOn")) / 1000).strftime('%Y-%m-%d %H:%M:%S'),
                "ticketId": None,
                "entityType": "ebMeter",
                "entityName": None,
                "lightpoint": None,
                "isDevice": None,
                "description": f"ebMeter {activity}",
                "photoProof": AssetService.construct_asset_image_link(
                    asset_name=request_params.get("replaceWith").get("auditImg")),
                "lampDetails": None,
                "poleDetails": None,
                "panelDetails": None,
                "luminodeDetails": None
            }
            return message
        except Exception as e:
            logger.exception(f"Error publishing data to Pub/Sub: {e}")
            return None

    @staticmethod
    def publish_activity_log_to_pubsub(message, topic_path):
        publisher = pubsub_v1.PublisherClient.from_service_account_json(SERVICE_ACCOUNT_KEY_PATH)
        # Publish the message to the specified topic
        message_future = publisher.publish(topic_path, data=json.dumps(message).encode('utf-8'))
        logger.info(f"Published message {message_future.result()} to {topic_path}")

    @staticmethod
    def publish_lamp_data_to_pubsub(message):
        publisher = pubsub_v1.PublisherClient.from_service_account_json(SERVICE_ACCOUNT_KEY_PATH)
        # Publish the message to the specified topic
        message_future = publisher.publish(PUBSUB_LAMP_TOPIC_PATH, data=json.dumps(message).encode('utf-8'))
        logger.info(f"Published message {message_future.result()} to {PUBSUB_LAMP_TOPIC_PATH}")

    @staticmethod
    def construct_asset(request_param: dict, label: str) -> dict or bool:
        for params in GW_DISPATCH_QUERY_PARAMS[0:2]:
            if params not in request_param and request_param.get(params) != []:
                return False
        else:
            return {'name': request_param.get('panelId'), 'type': request_param.get('gwType'), 'label': label}

    @staticmethod
    def asset_level_entity(entity_id: str, asset_type: list, relation_type: str, entity_type: str,
                           max_level: int) -> dict:
        try:
            entity_query = {
                "entityFields": [
                    {
                        "type": "ENTITY_FIELD",
                        "key": "name"
                    },
                    {
                        "type": "ENTITY_FIELD",
                        "key": "type"
                    },
                    {
                        "type": "ENTITY_FIELD",
                        "key": "label"
                    },
                    {
                        "type": "ENTITY_FIELD",
                        "key": "id"
                    }],
                "latestValues": [
                    {
                        "key": "poleSchema",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "lampWatts",
                        "type": "SERVER_ATTRIBUTE"
                    }
                ],
                "entityFilter": {
                    "type": "assetSearchQuery",
                    "rootEntity": {
                        "entityType": entity_type,
                        "id": entity_id
                    },
                    "direction": "FROM", "maxLevel": max_level, "fetchLastLevelOnly": True,
                    "relationType": relation_type, "assetTypes": asset_type
                },
                "pageLink": {
                    "dynamic": True, "page": 0, "pageSize": 10000,
                    "sortOrder": {
                        "key": {
                            "key": "name",
                            "type": "ENTITY_FIELD"
                        },
                        "direction": "ASC"
                    },
                    "textSearch": ""
                }
            }
            return entity_query
        except Exception as e:
            logger.exception("couldn't create a asset level query - %s" % str(e))

    @staticmethod
    def construct_asset_details(data: dict, asset_type: str) -> dict:
        asset_details = []
        try:
            for details in data:
                _name = details.get("latest").get("ENTITY_FIELD").get("name").get("value")
                _label = details.get("latest").get("ENTITY_FIELD").get("label").get("value")
                _label_portion = _label.split(">")
                _label = "%s-%s" % (_label_portion[0].strip(), _name.split("-")[-1])
                asset_details.append({
                    "name": _name,
                    "label": _label,
                    "id": details.get("entityId").get("id"),
                    "type": details.get("latest").get("ENTITY_FIELD").get("type").get("value")})
        except Exception as e:
            pass
        return {asset_type: asset_details}

    @staticmethod
    def create_asset_entity(request_param: str, data: dict, label: str) -> dict:
        if request_param.lower() == ILM_ASSET_TYPES[1].lower():
            lp_name = uuid.uuid3(uuid.NAMESPACE_URL, json.dumps({'lat': data.get("latitude"),
                                                                 'long': data.get("longitude"),
                                                                 'ts': data.get("installedOn"), 'type': 'LP'}))
            return {"customerId": {"entityType": OWNER_TYPE[0], "id": data.get("customerId")},
                    "name": str(lp_name), "type": ILM_ASSET_TYPES[1], "label": label}
        elif request_param.lower() == ILM_ASSET_TYPES[0].lower():
            name = uuid.uuid3(uuid.NAMESPACE_URL, json.dumps({'lat': data.get("latitude"),
                                                              'long': data.get("longitude"),
                                                              'ts': data.get("installedOn"), 'type': 'LMP'}))
            lamp = data.get("lampName")
            if "lampName" in data:
                if lamp == "":
                    name = uuid.uuid3(uuid.NAMESPACE_URL, json.dumps({'lat': data.get("latitude"),
                                                                      'long': data.get("longitude"),
                                                                      'ts': data.get("installedOn"), 'type': 'LMP'}))
                else:
                    name = lamp
            return {"customerId": {"entityType": OWNER_TYPE[0], "id": data.get("customerId")}, "name": str(name),
                    "type": ILM_ASSET_TYPES[0], "label": label}

    @staticmethod
    def create_lamp_asset(data: dict, detail: str) -> dict:
        return {"customerId": {"entityType": OWNER_TYPE[0], "id": data.get("customerId")}, "name": detail,
                "type": ILM_ASSET_TYPES[0], "label": detail}

    @staticmethod
    def construct_region_details(data: dict) -> dict:
        region_details = []
        for details in data:
            region_details.append({"name": details.get("name"),
                                   "id": details.get("id").get("id"),
                                   "customerId": details.get("ownerId").get("id")})
        return {"regions": region_details}

    @staticmethod
    def get_asset_owned_customer(data: dict, owner_type: str) -> dict:
        customer_details = {}
        try:
            if data.get("ownerId").get("entityType").lower() == owner_type.lower():
                customer_details = data.get("ownerId")
        except Exception as e:
            logger.exception("can't get asset owned customer - %s" % str(e))
        return customer_details

    @staticmethod
    def asset_lat_lon(entity_id: str, asset_type: list, relation_type: str, entity_type: str) -> dict:
        try:
            entity_query = {
                "entityFields": [
                    {
                        "type": "ENTITY_FIELD",
                        "key": "name"
                    },
                    {
                        "type": "ENTITY_FIELD",
                        "key": "type"
                    },
                    {
                        "type": "ENTITY_FIELD",
                        "key": "label"
                    }],
                "latestValues": [
                    {
                        "key": "latitude",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "longitude",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "slatitude",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "slongitude",
                        "type": "SERVER_ATTRIBUTE"
                    },
                ],
                "entityFilter": {
                    "type": "assetSearchQuery",
                    "rootEntity": {
                        "entityType": entity_type,
                        "id": entity_id
                    },
                    "direction": "FROM", "fetchLastLevelOnly": False, "relationType": relation_type,
                    "assetTypes": asset_type
                },
                "pageLink": {
                    "dynamic": True, "page": 0, "pageSize": 10000,
                    "sortOrder": {
                        "key": {
                            "key": "name",
                            "type": "ENTITY_FIELD"
                        },
                        "direction": "ASC"
                    },
                    "textSearch": ""
                }
            }
            return entity_query
        except Exception as e:
            logger.exception("can't create asset lat lon query - %s" % str(e))

    @staticmethod
    def construct_asset_lat_lon_details(data: dict) -> dict:
        lp_asset_details = []
        ccms_asset_details = []
        hub_asset_details = []
        for details in data:
            if details.get("latest").get("ENTITY_FIELD").get("type").get("value") == "lightPoint":
                lp_asset_details.append({
                    "name": details.get("latest").get("ENTITY_FIELD").get("name").get("value"),
                    "id": details.get("entityId").get("id"),
                    "latitude": details.get("latest").get("SERVER_ATTRIBUTE").get("latitude").get("value"),
                    "longitude": details.get("latest").get("SERVER_ATTRIBUTE").get("longitude").get("value")})
            elif details.get("latest").get("ENTITY_FIELD").get("type").get("value") == "ccms":
                ccms_asset_details.append({
                    "id": details.get("entityId").get("id"),
                    "name": details.get("latest").get("ENTITY_FIELD").get("name").get("value"),
                    "slatitude": details.get("latest").get("SERVER_ATTRIBUTE").get("slatitude").get("value"),
                    "slongitude": details.get("latest").get("SERVER_ATTRIBUTE").get("slongitude").get("value")})
            elif details.get("latest").get("ENTITY_FIELD").get("type").get("value") == "hub":
                hub_asset_details.append({
                    "id": details.get("entityId").get("id"),
                    "name": details.get("latest").get("ENTITY_FIELD").get("name").get("value"),
                    "slatitude": details.get("latest").get("SERVER_ATTRIBUTE").get("slatitude").get("value"),
                    "slongitude": details.get("latest").get("SERVER_ATTRIBUTE").get("slongitude").get("value")})

        return {"lightPoint": lp_asset_details, "ccms": ccms_asset_details, "hub": hub_asset_details}

    @staticmethod
    def create_pole_asset_name(data: dict) -> dict:
        return {"customerId": {"entityType": OWNER_TYPE[0], "id": data.get("customerId")},
                "name": str(data.get("name")), "type": ILM_ASSET_TYPES[5], "label": data.get("location")}

    @staticmethod
    def create_switch_point_asset_name(data: dict) -> dict:
        return {"customerId": {"entityType": OWNER_TYPE[0], "id": data.get("customerId")},
                "name": str(data.get("switchPointNumber")), "type": ILM_ASSET_TYPES[6], "label": data.get("location")}

    @staticmethod
    def create_transformer_asset_name(data: dict) -> dict:
        return {"customerId": {"entityType": OWNER_TYPE[0], "id": data.get("customerId")},
                "name": str(data.get("transformerNumber")), "type": ILM_ASSET_TYPES[7], "label": data.get("location")}

    @staticmethod
    def asset_details_query_construct(entity_id: str, entity_type: str) -> dict:
        try:
            entity_query = {
                "entityFields": [
                    {
                        "type": "ENTITY_FIELD",
                        "key": "name"
                    },
                    {
                        "type": "ENTITY_FIELD",
                        "key": "type"
                    },
                    {
                        "type": "ENTITY_FIELD",
                        "key": "label"
                    }],
                "latestValues": [
                    {
                        "key": "state",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "condition",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "type",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "latitude",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "longitude",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "slatitude",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "slongitude",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "wardName",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "zoneName",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "region",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "location",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "landmark",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "lampProfiles",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "connection",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "accuracy",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "armCount",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "installedBy",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "installedOn",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "clampDimension",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "lampWatts",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "dimmable",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "lampType",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "year",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "manufacturer",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "ebMeterNo",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "phase",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "simNo",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "type": "TIME_SERIES",
                        "key": "auditImg"
                    },
                    {
                        "type": "TIME_SERIES",
                        "key": "meterReadingOffset"
                    },
                    {
                        "key": "ebMeterReplacedBy",
                        "type": "TIME_SERIES"
                    },
                    {
                        "key": "ebMeterReplacedOn",
                        "type": "TIME_SERIES"
                    },
                    {
                        "key": "discomPoleNumber",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "height",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "span",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "clampDimension",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "vehicleAccessAvailable",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "earthingRequired",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "controlWireStatus",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "controlWireStatus",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "armDetails",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "manualSwitchControl",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "remarks",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "signalStrength",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "roadCategory",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "roadWidth",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "incomingTransmissionLine",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "incomingTransmissionType",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "bracketMountingHeight",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "locationDetails",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "assetType",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "roadType",
                        "type": "SERVER_ATTRIBUTE"
                    }
                ],
                "entityFilter": {
                    "type": "singleEntity",
                    "singleEntity": {
                        "id": entity_id,
                        "entityType": entity_type
                    }
                },
                "pageLink": {
                    "dynamic": True, "page": 0, "pageSize": 100,
                    "sortOrder": {
                        "direction": "ASC"
                    },
                    "textSearch": ""
                }
            }
            return entity_query
        except Exception as e:
            logger.exception("can't create asset details query - %s" % str(e))

    @staticmethod
    def construct_asset_relation_query(entity_id: str, entity_type: str, direction: str, relation_type: str) -> dict:
        try:
            entity_query = {
                "entityFields": [
                    {
                        "type": "ENTITY_FIELD",
                        "key": "name"
                    },
                    {
                        "type": "ENTITY_FIELD",
                        "key": "type"
                    },
                    {
                        "type": "ENTITY_FIELD",
                        "key": "label"
                    }],
                "latestValues": [
                    {
                        "key": "state",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "type",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "latitude",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "longitude",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "wardName",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "type": "TIME_SERIES",
                        "key": "auditImg"
                    },
                    {
                        "key": "zoneName",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "region",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "location",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "lampProfiles",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "connection",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "accuracy",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "armCount",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "installedBy",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "installedOn",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "clampDimension",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "dimmable",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "lampType",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "lampWatts",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "year",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "manufacturer",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "condition",
                        "type": "SERVER_ATTRIBUTE"
                    },
                ],
                "entityFilter": {
                    "type": "relationsQuery",
                    "rootEntity": {
                        "entityType": entity_type,
                        "id": entity_id
                    },
                    "direction": direction,
                    "fetchLastLevelOnly": False, "maxLevel": 1,
                    "filters": [
                        {
                            "relationType": relation_type,
                            "entityTypes": ["DEVICE", "ASSET"]
                        }
                    ]
                },
                "pageLink": {
                    "dynamic": True, "page": 0, "pageSize": 10000,
                    "sortOrder": {
                        "direction": "ASC"
                    },
                    "textSearch": ""
                }
            }
            return entity_query
        except Exception as e:
            logger.exception("can't create pole details query - %s" % str(e))

    @staticmethod
    def construct_ccms_details(data: list) -> dict:
        _result_dict = {}
        try:
            details = data[0]
            _entity_fields = details.get("latest").get("ENTITY_FIELD")
            for key, value in _entity_fields.items():
                _result_dict[key] = value.get("value")
            _server_attributes, _time_series = {}, {}
            _result_dict["id"] = details.get("entityId").get("id")
            if "SERVER_ATTRIBUTE" in details.get("latest"):
                _server_attributes = details.get("latest").get("SERVER_ATTRIBUTE")
            if "TIME_SERIES" in details.get("latest"):
                _time_series = details.get("latest").get("TIME_SERIES")
            _entity_type = _result_dict.get("type")
            if _entity_type in (GW_ASSET_TYPES[0], GW_ASSET_TYPES[2]):
                _result_dict["type"] = GW_ASSET_TYPES[0]
                _result_dict["ebMeter"] = {}
                for attribute, new_attribute in CCMS_EBMETER_ATTRIBUTES.items():
                    if attribute in _time_series:
                        _result_dict["ebMeter"][new_attribute] = _time_series.get(attribute).get("value")
                for attribute in CCMS_INSTALLATION_ATTRIBUTES + GW_DISPATCH_QUERY_PARAMS:
                    if attribute in _server_attributes:
                        if not attribute == "ebMeterNo":
                            _result_dict[attribute] = _server_attributes.get(attribute).get("value")
                        else:
                            _result_dict["ebMeter"]["name"] = _server_attributes.get(attribute).get("value")
            elif _entity_type == ILM_ASSET_TYPES[1]:
                for attribute in LIGHT_POINT_INSTALLATION_ATTRIBUTES:
                    if attribute in _server_attributes:
                        _result_dict[attribute] = _server_attributes.get(attribute).get("value")
        except Exception as e:
            logger.exception("relation construct - %s" % str(e))
        return _result_dict

    @staticmethod
    def query_lamp_pole_asset_relation_query(entity_id: str, entity_type: str, direction: str,
                                             relation_type: str, max_level: int) -> dict:
        try:
            entity_query = {
                "entityFields": [
                    {
                        "type": "ENTITY_FIELD",
                        "key": "name"
                    },
                    {
                        "type": "ENTITY_FIELD",
                        "key": "type"
                    },
                    {
                        "type": "ENTITY_FIELD",
                        "key": "label"
                    }],
                "latestValues": [
                    {
                        "key": "state",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "condition",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "type",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "latitude",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "longitude",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "wardName",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "zoneName",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "region",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "location",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "lampProfiles",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "connection",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "accuracy",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "armCount",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "installedBy",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "installedOn",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "clampDimension",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "landmark",
                        "type": "SERVER_ATTRIBUTE"
                    },
                    {
                        "key": "auditImg",
                        "type": "TIME_SERIES"
                    },
                ],
                "entityFilter": {
                    "type": "relationsQuery",
                    "rootEntity": {
                        "entityType": entity_type,
                        "id": entity_id
                    },
                    "direction": direction,
                    "fetchLastLevelOnly": False, "maxLevel": max_level,
                    "filters": [
                        {
                            "relationType": relation_type,
                            "entityTypes": ["DEVICE", "ASSET"]
                        }
                    ]
                },
                "pageLink": {
                    "dynamic": True, "page": 0, "pageSize": 100,
                    "sortOrder": {
                        "direction": "ASC"
                    },
                    "textSearch": ""
                }
            }
            return entity_query
        except Exception as e:
            logger.exception("can't create pole details query - %s" % str(e))

    @staticmethod
    def lamp_details_construct(data: list):
        lamp_detail = {}
        for details in data:
            lamp_detail = {"name": details.get("latest").get("ENTITY_FIELD").get("name").get("value"),
                           "id": details.get("entityId").get("id"),
                           "type": details.get("latest").get("ENTITY_FIELD").get("type").get("value"),
                           "label": details.get("latest").get("ENTITY_FIELD").get("label").get("value")}
            for attribute in LAMP_INSTALLATION_ATTRIBUTES:
                lamp_detail[attribute] = details.get("latest").get("SERVER_ATTRIBUTE").get(attribute).get("value")
        return lamp_detail

    @staticmethod
    def pole_details_construct(data: list, pole_name: str = None) -> dict:
        pole_detail = {}
        img_name = ""
        for details in data:
            pole_detail = {"name": details.get("latest").get("ENTITY_FIELD").get("name").get("value"),
                           "id": details.get("entityId").get("id"),
                           "asset_type": details.get("latest").get("ENTITY_FIELD").get("type").get("value"),
                           "pole_type": details.get("latest").get("SERVER_ATTRIBUTE").get("type").get("value")}
            for attribute in POLE_INSTALLATION_ATTRIBUTES[1:]:
                if attribute in details.get("latest").get("SERVER_ATTRIBUTE"):
                    pole_detail[attribute] = details.get("latest").get("SERVER_ATTRIBUTE").get(attribute).get("value")
            if details.get("latest").get("TIME_SERIES").get("auditImg"):
                img_name = details.get("latest").get("TIME_SERIES").get("auditImg").get("value")
            else:
                img_name = pole_detail["name"] + ".png"
        if pole_name:
            s3_image_url_construct = AssetService.construct_asset_image_link(asset_name=img_name)
            pole_detail["auditPicS3Url"] = s3_image_url_construct
        return pole_detail

    def construct_ward_response(self, data: list) -> list:
        ward_details = []
        for details in data:
            lamp_query = AssetService.asset_level_entity(entity_id=details.get("to").get("id"),
                                                         asset_type=["lamp"], relation_type=RELATION_TYPES[2],
                                                         entity_type=ENTITY_TYPE[1], max_level=1)
            ilm_query = DeviceService.asset_level_device_entity(entity_id=details.get("to").get("id"),
                                                                device_type=["ilm", "ilm-4g"],
                                                                relation_type=RELATION_TYPES[1],
                                                                entity_type=ENTITY_TYPE[1])
            lamp_relation = self.find_entities_by_query(query=lamp_query)
            ilm_relation = self.find_entities_by_query(query=ilm_query)
            lamp_data = {"lightPointId": details.get("to").get("id")}
            lamp_data.update({"ilm": ilm_relation["data"][0]["latest"]["ENTITY_FIELD"]["name"]["value"] if ilm_relation[
                                                                                                               "data"] and
                                                                                                           ilm_relation[
                                                                                                               "data"][
                                                                                                               0] else ""})
            if "data" in lamp_relation and lamp_relation["data"]:
                for lamp in lamp_relation["data"]:
                    lamp_data.update({"name": lamp.get("latest").get("ENTITY_FIELD").get("name").get("value"),
                                      "id": lamp.get("entityId").get("id"),
                                      "type": lamp.get("latest").get("ENTITY_FIELD").get("type").get("value"),
                                      "label": lamp.get("latest").get("ENTITY_FIELD").get("label").get("value")})
            else:
                lamp_data.update({"name": "",
                                  "id": "",
                                  "type": "",
                                  "label": ""})
            ward_details.append(lamp_data)
        return ward_details

    @staticmethod
    def construct_asset_installation_history(user: str, asset_id: str, asset_type: str, relation_type: str):
        try:
            data = {
                "entityFilter": {
                    "type": "assetSearchQuery",
                    "rootEntity": {
                        "entityType": ENTITY_TYPE[1],
                        "id": asset_id
                    },
                    "direction": "FROM", "fetchLastLevelOnly": True,
                    "relationType": relation_type,
                    "assetTypes": [asset_type]
                },
                "entityFields": [
                    {
                        "type": "ENTITY_FIELD",
                        "key": "name"
                    },
                    {
                        "type": "ENTITY_FIELD",
                        "key": "type"
                    }
                ],
                "latestValues": [
                    {
                        "type": "SERVER_ATTRIBUTE",
                        "key": "installedOn"
                    },
                    {
                        "type": "SERVER_ATTRIBUTE",
                        "key": "installedBy"
                    },
                ],
                "keyFilters": [{
                    "key": {
                        "type": "SERVER_ATTRIBUTE",
                        "key": "installedBy"
                    },
                    "valueType": "STRING",
                    "predicate": {
                        "operation": "EQUAL",
                        "value": {
                            "defaultValue": user,
                            "dynamicValue": None
                        },
                        "type": "STRING"
                    }
                }
                ],
                "pageLink": {
                    "page": 0,
                    "pageSize": 100000,
                    "sortOrder": {
                        "key": {
                            "key": "name",
                            "type": "ENTITY_FIELD"
                        },
                        "direction": "DESC"
                    },
                    "textSearch": ""
                }
            }
            return data
        except Exception as e:
            logger.exception("can't create installation history query - %s" % str(e))

    @staticmethod
    def construct_date_wise_asset_installation_count(details: dict):
        today = date.today()
        data = {today.strftime("%d-%m-%Y"): 0}
        try:
            if details:
                for asset in details:
                    ts = int(asset['latest']['SERVER_ATTRIBUTE']['installedOn']['value'])
                    ss = datetime.fromtimestamp(ts / 1000)
                    _date = ss.strftime('%d-%m-%Y')
                    if _date in data:
                        data[_date] += 1
                    else:
                        data[_date] = 1
                sorted_data = dict(
                    sorted(data.items(), key=lambda x: datetime.strptime(x[0], "%d-%m-%Y"), reverse=True))
                sorted_data = dict(zip(list(sorted_data.keys())[:5], list(sorted_data.values())[:5]))
                count = sum(data.values())
                sorted_data = {
                    "User_history": sorted_data,
                    "Total_count": count
                }
            else:
                sorted_data = {
                    "User_history": data,
                    "Total_count": 0
                }
            return sorted_data
        except Exception as e:
            logger.exception("can't construct date wise asset installation count - %s" % str(e))

    def get_relation_info_fromid(self, from_id: str, from_type: str, relation_type: str):
        url = BASE_URL + self.get_relation_info_fromid_url.format(from_id=from_id,
                                                                  from_type=from_type, relation_type=relation_type)
        return RequestResponseHandler.get_request(url=url, header=self.header, data='', instance=self.instance)

    def get_telemetry(self, entity_id, entity_type, key):
        url = BASE_URL + self.get_telemetry_data_url.format(entity_type=entity_type,
                                                            entity_id=entity_id, key=key)
        return RequestResponseHandler.get_request(url=url, header=self.header, data='', instance=self.instance)

    def get_relation_info_toid(self, to_id: str, to_type: str, relation_type: str):
        url = BASE_URL + self.get_relation_info_toid_url.format(to_id=to_id,
                                                                to_type=to_type, relation_type=relation_type)
        return RequestResponseHandler.get_request(url=url, header=self.header, data='', instance=self.instance)

    def search_entity_query(self, data: str):
        url = BASE_URL + self.search_entity_query_url
        return RequestResponseHandler.post_request(url=url, header=self.header, data=data, instance=self.instance)

    def get_user_by_name(self, user_name: str) -> dict:
        url = BASE_URL + self.get_user_by_name_url.format(user_name=user_name)
        return RequestResponseHandler.get_request(url=url, header=self.header, data='', instance=self.instance)

    def is_pole_arms_free_to_install(self, pole_id=None, installation_type=None, lp_id=None):
        """
        pole_id:
        installation_type: either one of ["ilm", "lamp"]
        """
        try:
            lp_id = None if lp_id == '' else lp_id

            logger.info("Validating arm is free from pole %s to install %s" % (pole_id, installation_type))
            # Fetching total Arm count of a Pole
            _pole_arm_count_attr = self.get_entity_attribute(
                entity_type=ENTITY_TYPE[1], entity_id=pole_id, scope=ATTRIBUTE_SCOPES[1], keys="armCount")
            _pole_arm_count = int(_pole_arm_count_attr[0]["value"]) if _pole_arm_count_attr else 0

            # Fetching Installed Arm's count of a Pole
            _pole_relation = self.get_relation_info_fromid(
                from_id=pole_id, from_type=ENTITY_TYPE[1], relation_type=RELATION_TYPES[5])
            _installed_arms_count = len(_pole_relation)

            if not lp_id:
                if _installed_arms_count >= _pole_arm_count:
                    return False

            for item in _pole_relation:
                _lp_id = item.get("to", {}).get("id")
                _ilm_attr = self.get_entity_attribute(
                    entity_type=ENTITY_TYPE[1], entity_id=_lp_id, scope=ATTRIBUTE_SCOPES[1], keys=installation_type)
                if not _ilm_attr or _ilm_attr[0].get('value') == '-':
                    _installed_arms_count = _installed_arms_count - 1
            # Is valid check
            return _installed_arms_count < _pole_arm_count
        except Exception as e:
            logger.exception("Checking is pole arms free ... %s" % str(e))
        return False


class LampService(TbLampController):
    def __init__(self, token: str, instance: object):
        self.header = {'content-type': 'application/json', 'X-Authorization': 'Bearer ' + token}
        self.instance = instance

    def save_token(self, token: str) -> None:
        self.header['X-Authorization'] = token

    @staticmethod
    def construct_lamp_entity(data: dict, label: str):
        name = uuid.uuid3(uuid.NAMESPACE_URL, json.dumps({'lat': data.get("latitude"),
                                                          'long': data.get("longitude"),
                                                          'ts': data.get("installedOn"), 'type': 'LMP'}))
        lamp = data.get("name")
        if "name" in data:
            if lamp == "":
                name = uuid.uuid3(uuid.NAMESPACE_URL, json.dumps({'lat': data.get("latitude"),
                                                                  'long': data.get("longitude"),
                                                                  'ts': data.get("installedOn"), 'type': 'LMP'}))
            else:
                name = lamp
        return {"customerId": {"entityType": OWNER_TYPE[0], "id": data.get("customerId")}, "name": str(name),
                "type": ILM_ASSET_TYPES[0], "label": label}

    @staticmethod
    def install_lamp(request_params, asset_service: AssetService, device_service, request_handler, customer_service):
        try:
            logger.info("install_lamp")
            request_params = request_params
            request_handler = request_handler
            device_service = device_service
            asset_service = asset_service
            img_data = request_params.get('auditImg')
            ward_id = request_params.get("wardId")
            customer_id = request_params.get("customerId")
            region = request_params.get('region')
            lamp_details = request_params.get("lamp")
            light_point_attributes = {}
            install_lamp_attributes = {}
            pole_id = ""
            default_customer_id = {}
            if customer_id:
                default_customer_id = customer_service.get_customer_by_id(owner_type=OWNER_TYPE[0],
                                                                owner_id=customer_id)
            if "poleId" in request_params:
                pole_id = request_params.get("poleId")
            telemetry_data = {"ts": request_params.get("installedOn"),
                              "values": {"auditImg": img_data, "pkt": 102, "activity": "Install"}}
            pole_data = {}
            if request_params.get("pole"):
                pole_data = request_params.get("pole")
            if request_params.get("customerId") and lamp_details.get("name") and ward_id:
                lamp_det = "{manufacturer}-{wattage}-{type}-{dimmable}-{year}-{name}".format(
                    manufacturer=str(lamp_details.get("manufacturer")),
                    wattage=str(lamp_details.get("lampWatts")).zfill(4), type=str(lamp_details.get("lampType")),
                    dimmable=str(lamp_details.get("dimmable")), year=str(lamp_details.get("year")),
                    name=str(lamp_details.get("name")))
                get_lamp = asset_service.get_entity_by_name(entity_type=ENTITY_TYPE[1], name=lamp_det)
                logger.info(f"Lamp Installation issue: {lamp_det} : {get_lamp}")
                if get_lamp.get("status") == 400 or get_lamp.get("status") == 404:
                    construct_new_lamp_asset = asset_service.create_lamp_asset(data=request_params, detail=lamp_det)
                    create_lamp = asset_service.create_and_update_asset_name(details=construct_new_lamp_asset)
                    lamp_asset_id, lamp_asset_name = create_lamp.get("id"), create_lamp.get("name")
                    install_lamp_attributes.update(LAMP_INSTALLED_STATE_CONDITION)
                else:
                    lamp_asset_id, lamp_asset_name = get_lamp.get("id").get("id"), get_lamp.get("name")
                    to_relation_query = asset_service.query_lamp_pole_asset_relation_query(
                        entity_id=lamp_asset_id, entity_type=ENTITY_TYPE[1], direction=ENTITY_RELATIONS[1],
                        relation_type=RELATION_TYPES[5], max_level=2)
                    to_relation_detail = device_service.find_entities_by_query(query=to_relation_query)
                    if "data" in to_relation_detail and to_relation_detail["data"]:
                        from_info = asset_service.pole_details_construct(data=to_relation_detail["data"])
                        customer = customer_service.get_customer_by_id(owner_type=OWNER_TYPE[0],
                                                                       owner_id=get_lamp.get("ownerId").get("id"))
                        message = asset_service.construct_pubsub_lamp_install_report_data(
                            lamp_data=from_info, lamp_det=lamp_det, pole_data=from_info, details=lamp_details,
                            remove=True, customer_name=customer.get("name"), img_name=img_data)
                        asset_service.publish_lamp_data_to_pubsub(message=message)
                    device_service.get_and_remove_from_and_to_relations(entity_id=lamp_asset_id,
                                                                        entity_type=ENTITY_TYPE[1])
                    install_lamp_attributes.update(INSTALLED_STATE)
                    if get_lamp.get("ownerId").get("id") != request_params.get("customerId"):
                        asset_service.change_owner(owner_type=OWNER_TYPE[0], owner_id=request_params.get("customerId"),
                                                   entity_id=lamp_asset_id)
                if request_params.get("lightPointId") != "":
                    light_point_asset_id = request_params.get("lightPointId")
                    light_point_details = asset_service.get_entity_by_id(entity_type=ENTITY_TYPE[1],
                                                                         entity_id=light_point_asset_id)
                    light_point_name = light_point_details.get("name")
                    light_point_attributes.update(device_service.construct_attributes(
                        request_attributes=request_params, valid_keys=LIGHT_POINT_INSTALLATION_ATTRIBUTES[7:9]))
                else:
                    construct_new_light_point_asset = asset_service.create_asset_entity(
                        request_param=ILM_ASSET_TYPES[1], data=request_params, label="")
                    light_point = asset_service.create_and_update_asset_name(details=construct_new_light_point_asset)
                    light_point_asset_id, light_point_name = light_point.get("id"), light_point.get("name")
                    light_point_attributes.update(device_service.construct_attributes(
                        request_attributes=request_params, valid_keys=LIGHT_POINT_INSTALLATION_ATTRIBUTES))
                    light_point_attributes.update(INSTALLED_STATE)
                install_lamp_attributes.update(device_service.construct_attributes(
                    request_attributes=lamp_details, valid_keys=LAMP_INSTALLATION_ATTRIBUTES))
                pole_data = request_params.get("pole") if request_params.get("pole") else {}
                light_point_add_attributes = device_service.create_attributes(
                    entity_type=ILM_ASSET_TYPES[1], lamp=lamp_asset_name, ilm="-",
                    pole_name=pole_data.get("name") if pole_data else "")
                lamp_add_attributes = device_service.create_attributes(
                    entity_type=ILM_ASSET_TYPES[0], ilm="-", lp=light_point_name,
                    pole_name=pole_data.get("name") if pole_data else "")
                install_lamp_attributes.update(device_service.construct_attributes(
                    request_attributes=request_params, valid_keys=ILM_INSTALLATION_ATTRIBUTES[7:]))
                install_lamp_attributes.update(lamp_add_attributes)
                light_point_attributes.update(light_point_add_attributes)
                light_point_attributes.update({LIGHT_POINT_INSTALLATION_ATTRIBUTES[9]: lamp_details.get("lampWatts")})
                asset_service.create_entity_relation(from_id=ward_id, from_type=ENTITY_TYPE[1],
                                                     to_id=light_point_asset_id, to_type=ENTITY_TYPE[1],
                                                     relation_type=RELATION_TYPES[0])
                message = asset_service.construct_pubsub_lamp_install_report_data(
                    lamp_data=request_params,
                    lamp_det=lamp_det,
                    pole_data=request_params.get("pole"),
                    details=lamp_details, remove=False,
                    img_name=img_data, 
                    customer_name=default_customer_id.get("name"))
                asset_service.publish_lamp_data_to_pubsub(message=message)
                lamp_lp_relation = asset_service.create_entity_relation(
                    from_id=light_point_asset_id, from_type=ENTITY_TYPE[1], to_id=lamp_asset_id,
                    to_type=ENTITY_TYPE[1], relation_type=RELATION_TYPES[2],
                    additional_info={"lamp": install_lamp_attributes, "lightPoint": light_point_attributes})
                # Placing Lamp under region group
                asset_service.place_entity_under_group(
                    entity_ids=[lamp_asset_id, light_point_asset_id], owner_type=OWNER_TYPE_CUSTOMER,
                    owner_id=customer_id, group_type=ENTITY_TYPE_ASSET, group_name=region)
                try:
                    if pole_id:
                        asset_service.place_entity_under_group(
                            entity_ids=[pole_id], owner_type=OWNER_TYPE_CUSTOMER, owner_id=customer_id,
                            group_type=ENTITY_TYPE_ASSET, group_name=region)
                except Exception as e:
                    logger.exception("Placing pole under region failure: ", e)
                if lamp_lp_relation.get("status") == 200 and pole_id:
                    request_handler.update_service_response(
                        asset_service.create_entity_relation(from_id=pole_id, from_type=ENTITY_TYPE[1],
                                                             to_id=light_point_asset_id, to_type=ENTITY_TYPE[1],
                                                             relation_type=RELATION_TYPES[5],
                                                             additional_info={"lamp": message}))
                else:
                    request_handler.update_service_response(
                        {"status": 400, "message": "Can't create relation between lamp and lightPoint"})
                asset_service.update_telemetry(entity_type=ENTITY_TYPE[1], entity_id=lamp_asset_id,
                                               data=telemetry_data)
                asset_service.update_telemetry(entity_type=ENTITY_TYPE[1], entity_id=light_point_asset_id,
                                               data=telemetry_data)
                light_point_attributes.clear()
                install_lamp_attributes.clear()
            else:
                request_handler.update_service_response({"status": 400, "message": "Invalid request body"})
        except Exception as e:
            request_handler.update_error_response(str(e))
            logger.exception("couldn't install lamp %s" % str(e))
        return request_handler.get_service_response()

    @staticmethod
    def replace_lamp(request_params, asset_service, device_service, lamp_service, request_handler, customer_service):
        try:
            logger.info(f"\n\nReplacing Lamp... {request_params}")
            old_lamp = request_params.get("remove")
            region = request_params.get("region")
            customer_id = request_params.get("customerId")
            lamp_id = old_lamp.get("lampId")
            img_data = request_params.get('auditImg')

            lamp_attr = asset_service.get_entity_attribute(entity_type=ENTITY_TYPE[1], entity_id=lamp_id,
                                                           scope=ATTRIBUTE_SCOPES[1],
                                                           keys="manufacturer,year,lampType,lampWatts,dimmable")
            lamp_details = asset_service.get_entity_by_id(entity_type=ENTITY_TYPE[1], entity_id=lamp_id)
            customer_det = customer_service.get_customer_by_id(owner_type=OWNER_TYPE[0],
                                                               owner_id=lamp_details.get("ownerId").get("id"))
            remove_attr = {}
            remove_attr1 = {}
            telemetry_data = {"ts": int(time.time()) * 1000,
                              "values": {"auditImg": img_data, "pkt": 102, "activity": "Remove"}}
            pole_data = {}
            if request_params.get("pole"):
                pole_data = request_params.get("pole")
            if request_params.get("poleId"):
                pole_attr = asset_service.get_entity_attribute(entity_type=ENTITY_TYPE[1],
                                                               entity_id=request_params.get("poleId"),
                                                               scope=ATTRIBUTE_SCOPES[1],
                                                               keys="armCount,type,connection,lampProfiles")
                pole_name = asset_service.get_entity_by_id(entity_type=ENTITY_TYPE[1],
                                                           entity_id=request_params.get("poleId"))
                remove_attr1["name"] = pole_name.get("name")
                for attribute in pole_attr:
                    remove_attr1[attribute.get("key")] = attribute.get("value")
            else:
                pole_attr = asset_service.query_lamp_pole_asset_relation_query(entity_id=lamp_id,
                                                                               entity_type=ENTITY_TYPE[1],
                                                                               direction=ENTITY_RELATIONS[1],
                                                                               relation_type=RELATION_TYPES[5],
                                                                               max_level=2)
                pole_attr_const = device_service.find_entities_by_query(query=pole_attr)
                remove_attr1.update(asset_service.pole_details_construct(data=pole_attr_const["data"]))
            if request_params.get("lightPointId"):
                light_point_id = request_params.get("lightPointId")
            else:
                light_point = device_service.get_entity_to_relation_info(to_id=lamp_id, to_type=ENTITY_TYPE[1])
                light_point_id = light_point[0]["from"]["id"]
            light_point_attr = {"lamp": "-", "state": "INSTALLED", "uid": "-", 'lampWatts': "-"}
            if request_params.get('lamp', {}).get('lampWatts'):
                _lamp_watts = request_params.get('lamp').get('lampWatts')
                light_point_attr = {"lamp": "-", "state": "INSTALLED", "uid": "-", 'lampWatts': _lamp_watts}
            lp_attr = asset_service.get_entity_attribute(
                entity_type=ENTITY_TYPE[1], entity_id=light_point_id, scope=ATTRIBUTE_SCOPES[1],
                keys="installedOn,installedBy,latitude,longitude,landmark,zoneName,wardName,region")
            for attr in lp_attr:
                remove_attr[attr.get("key")] = attr.get("value")
            old_lamp.update(remove_attr)
            message = asset_service.construct_pubsub_lamp_install_report_data(
                lamp_data=old_lamp, lamp_det=lamp_details.get("name"), details=lamp_attr, pole_data=remove_attr1,
                remove=True, customer_name=customer_det["name"], img_name=img_data)
            device_service.get_and_remove_to_relations(to_id=lamp_id, to_type=ENTITY_TYPE[1],
                                                       removal_type=ENTITY_RELATIONS[0].lower(), removal=True)
            device_service.update_entity_attribute(entity_type=ENTITY_TYPE[1], entity_id=light_point_id,
                                                   data=json.dumps(light_point_attr), scope=ATTRIBUTE_SCOPES[1])
            device_service.update_entity_attribute(entity_type=ENTITY_TYPE[1], entity_id=lamp_id,
                                                   scope=ATTRIBUTE_SCOPES[1],
                                                   data=json.dumps(REPLACE_AND_REMOVE_DEVICE_STATE_AND_CONDITION))

            device_service.delete_entity_attribute(entity_type=ENTITY_TYPE[1], entity_id=light_point_id,
                                                   scope=ATTRIBUTE_SCOPES[1],
                                                   keys="commissionedBy,uid")
            try:
                asset_service.publish_lamp_data_to_pubsub(message=message)
            except Exception as e:
                logger.exception("Error publishing lamp data to pubsub: %s", e)

            lamp_attributes = {}
            asset_service.update_telemetry(entity_type=ENTITY_TYPE[1], entity_id=lamp_id,
                                           data=telemetry_data)
            lp_to_relation = asset_service.find_by_to_relations(to_type=ENTITY_TYPE[1], to_id=light_point_id,
                                                                relation_type=RELATION_TYPES[3])
            if lp_to_relation:
                for relation in lp_to_relation:
                    asset_service.remove_entity_relation(from_id=relation["from"]["id"],
                                                         from_type=relation["from"]["entityType"],
                                                         to_type=relation["to"]["entityType"],
                                                         to_id=relation["to"]["id"], relation_type=relation["type"])
            asset_service.update_telemetry(entity_type=ENTITY_TYPE[1], entity_id=light_point_id,
                                           data=telemetry_data)
            # to remove old lamp (in replacement as well as removal activity)
            try:
                lamp_info = asset_service.get_entity_info_by_id(entity_type=ENTITY_TYPE_ASSET, entity_id=lamp_id)
                for group in lamp_info.get("groups", []):
                    rem_region = group.get("name")
                    rem_customer_id = lamp_info.get("ownerId", {}).get("id")
                    if rem_customer_id != customer_id:
                        asset_service.remove_entity_under_group(
                            entity_ids=[lamp_id], owner_type=OWNER_TYPE_CUSTOMER,
                            owner_id=rem_customer_id, group_type=ENTITY_TYPE_ASSET, group_name=rem_region)
                    else:
                        if rem_region != region:
                            asset_service.remove_entity_under_group(
                                entity_ids=[lamp_id], owner_type=OWNER_TYPE_CUSTOMER,
                                owner_id=customer_id, group_type=ENTITY_TYPE_ASSET, group_name=rem_region)
                        else:
                            asset_service.remove_entity_under_group(
                                entity_ids=[lamp_id], owner_type=OWNER_TYPE_CUSTOMER,
                                owner_id=customer_id, group_type=ENTITY_TYPE_ASSET, group_name=region)
            except Exception as e:
                logger.exception("removing lamp from regional group")
            if request_params.get("lamp") or request_params.get("lightPointId"):
                light_point_det = asset_service.get_entity_by_id(entity_type=ENTITY_TYPE[1], entity_id=light_point_id)
                from_relation_query = device_service.device_relations(entity_id=light_point_id, entity_type=ENTITY_TYPE[1],
                                                                    direction=ENTITY_RELATIONS[0],
                                                                    relation_type=RELATION_TYPES[1])
                from_relation_detail = device_service.find_entities_by_query(query=from_relation_query)
                ilm_name = ""
                ilm_id = ""
                if "data" in from_relation_detail and from_relation_detail["data"]:
                    from_info = device_service.relation_construct(data=from_relation_detail["data"])
                    ilm_id = from_info["id"]
                    ilm_name = from_info["name"]
                new_lamp_details = request_params.get("lamp")
                if new_lamp_details:
                    lamp_det = "{manufacturer}-{wattage}-{type}-{dimmable}-{year}-{name}".format(
                        manufacturer=str(new_lamp_details.get("manufacturer")),
                        wattage=str(new_lamp_details.get("lampWatts")).zfill(4),
                        type=str(new_lamp_details.get("lampType")),
                        dimmable=str(new_lamp_details.get("dimmable")), year=str(new_lamp_details.get("year")),
                        name=str(new_lamp_details.get("name")))
                    get_lamp = asset_service.get_entity_by_name(entity_type=ENTITY_TYPE[1], name=lamp_det)
                    if get_lamp.get("status") == 400 or get_lamp.get("status") == 404:
                        construct_new_lamp_asset = asset_service.create_lamp_asset(data=request_params, detail=lamp_det)
                        create_lamp = asset_service.create_and_update_asset_name(details=construct_new_lamp_asset)
                        lamp_asset_id, lamp_asset_name = create_lamp.get("id"), create_lamp.get("name")
                        lamp_attributes.update(LAMP_INSTALLED_STATE_CONDITION)
                        lamp_attributes.update(device_service.construct_attributes(
                            request_attributes=new_lamp_details, valid_keys=LAMP_INSTALLATION_ATTRIBUTES))
                    else:
                        lamp_asset_id, lamp_asset_name = get_lamp.get("id").get("id"), get_lamp.get("name")
                        to_relation_query = asset_service.query_lamp_pole_asset_relation_query(
                            entity_id=lamp_asset_id, entity_type=ENTITY_TYPE[1], direction=ENTITY_RELATIONS[1],
                            relation_type=RELATION_TYPES[5], max_level=2)
                        to_relation_detail = device_service.find_entities_by_query(query=to_relation_query)
                        if "data" in to_relation_detail and to_relation_detail["data"]:
                            from_info = asset_service.pole_details_construct(data=to_relation_detail["data"])
                            customer = customer_service.get_customer_by_id(owner_type=OWNER_TYPE[0],
                                                                        owner_id=get_lamp.get("ownerId").get("id"))
                            message = asset_service.publish_pole_and_lamp_construct_report(
                                request_data=from_info, lamp_det=lamp_det, details=new_lamp_details, activity="Removal",
                                customer_details=customer, entity_name=lamp_det, entity_id=lamp_asset_id,
                                entity_type="lamp", img_name=request_params.get("auditImg"))
                            asset_service.publish_lamp_data_to_pubsub(message=message,
                                                                    topic_path=PUBSUB_ACTIVITY_LOG_TOPIC_PATH)
                        device_service.get_and_remove_from_and_to_relations(entity_id=lamp_asset_id,
                                                                            entity_type=ENTITY_TYPE[1])
                        lamp_attributes.update(INSTALLED_STATE)
                        if get_lamp.get("ownerId").get("id") != request_params.get("customerId"):
                            asset_service.change_owner(owner_type=OWNER_TYPE[0],
                                                    owner_id=request_params.get("customerId"),
                                                    entity_id=lamp_asset_id)
                else:
                    construct_new_lamp_asset = lamp_service.construct_lamp_entity(data=request_params,
                                                                                label=ilm_name)
                    lamp_attributes.update(LAMP_INSTALLED_STATE_CONDITION)
                    create_lamp = asset_service.create_and_update_asset_name(details=construct_new_lamp_asset)
                    lamp_asset_id, lamp_asset_name = create_lamp.get("id"), create_lamp.get("name")
                lamp_attributes.update(device_service.construct_attributes(request_attributes=request_params,
                                                                        valid_keys=ILM_INSTALLATION_ATTRIBUTES[7:]))
                pole_data = request_params.get("pole") if request_params.get("pole") else {}
                ilm_add_attributes = device_service.create_attributes(
                    entity_type=DEVICE_TYPES[1], lp=light_point_det.get("name"), lamp=lamp_asset_name,
                    pole_name=pole_data.get("name") if pole_data else "")
                light_point_add_attributes = device_service.create_attributes(
                    entity_type=ILM_ASSET_TYPES[1], ilm=ilm_name, lamp=lamp_asset_name,
                    pole_name=pole_data.get("name") if pole_data else "")
                lamp_add_attributes = device_service.create_attributes(
                    ilm=ilm_name, entity_type=ILM_ASSET_TYPES[0], lp=light_point_det.get("name"),
                    pole_name=pole_data.get("name") if pole_data else "")
                lamp_attributes.update(lamp_add_attributes)

                device_service.update_entity_attribute(entity_type=ENTITY_TYPE[0],
                                                    entity_id=ilm_id, scope=ATTRIBUTE_SCOPES[1],
                                                    data=json.dumps(ilm_add_attributes))
                device_service.update_entity_attribute(entity_type=ENTITY_TYPE[1],
                                                    entity_id=light_point_id, scope=ATTRIBUTE_SCOPES[1],
                                                    data=json.dumps(light_point_add_attributes))
                device_service.update_entity_attribute(entity_type=ENTITY_TYPE[1], entity_id=lamp_asset_id,
                                                    scope=ATTRIBUTE_SCOPES[1], data=json.dumps(lamp_attributes))
                attr = {}
                if request_params.get("poleId"):
                    pole_det = asset_service.asset_details_query_construct(entity_id=request_params.get("poleId"),
                                                                        entity_type=ENTITY_TYPE[1])
                    pole_attr_const = device_service.find_entities_by_query(query=pole_det)
                    attr.update(asset_service.pole_details_construct(data=pole_attr_const["data"]))
                message = asset_service.construct_pubsub_lamp_install_report_data(
                    lamp_data=request_params, lamp_det=lamp_asset_name, pole_data=attr, details=new_lamp_details,
                    img_name=img_data, customer_name=customer_det["name"])
                asset_service.create_entity_relation(from_id=light_point_id, from_type=ENTITY_TYPE[1],
                                                    to_id=lamp_asset_id, to_type=ENTITY_TYPE[1],
                                                    relation_type=RELATION_TYPES[2], additional_info={"lamp": message})
                telemetry_data = {"ts": int(time.time()) * 1000,
                                "values": {"auditImg": img_data, "pkt": 102, "activity": "Replace"}}
                asset_service.update_telemetry(entity_type=ENTITY_TYPE[1], entity_id=lamp_asset_id,
                                            data=telemetry_data)
                asset_service.update_telemetry(entity_type=ENTITY_TYPE[1], entity_id=light_point_id,
                                            data=telemetry_data)
                asset_service.publish_lamp_data_to_pubsub(message=message)
                # placing lamp under regional group
                try:
                    asset_service.place_entity_under_group(
                        entity_ids=[lamp_asset_id], owner_type=OWNER_TYPE_CUSTOMER,
                        owner_id=customer_id, group_type=ENTITY_TYPE_ASSET, group_name=region)
                except Exception as e:
                    logger.exception("placing lamp under regional group", e)
        except Exception as e:
            request_handler.update_error_response(str(e))
            logger.exception("can't replace the lamp - %s" % str(e))
        return request_handler.get_service_response()


class CustomerService(TbCustomerController):

    def __init__(self, token: str, instance: object):
        self.header = {'content-type': 'application/json', 'X-Authorization': 'Bearer ' + token}
        self.instance = instance

    def save_token(self, token: str) -> None:
        self.header['X-Authorization'] = token

    def get_customer_by_id(self, owner_type: str, owner_id: str) -> dict or None:
        url = BASE_URL + self.get_owner_by_id_url.format(owner_type=owner_type.lower(), owner_id=owner_id)
        return RequestResponseHandler.get_request(url=url, header=self.header, data='', instance=self.instance)

    def get_tenant_by_id(self, owner_type: str, owner_id: str):
        url = BASE_URL + self.get_owner_by_id_url.format(owner_type=owner_type.lower(), owner_id=owner_id)
        return RequestResponseHandler.get_request(url=url, header=self.header, data='', instance=self.instance)

    def get_all_customers(self):
        url = BASE_URL + self.get_all_customers_url.format()
        return RequestResponseHandler.get_request(url=url, header=self.header, data='', instance=self.instance)

    def get_all_customer_assets(self, customer_id: str, asset_type: str):
        url = BASE_URL + self.get_all_customer_assets_url.format(customer_id=customer_id, asset_type=asset_type,
                                                                 page_size=1000, page=0)
        return RequestResponseHandler.get_request(url=url, header=self.header, data='', instance=self.instance)

    @staticmethod
    def customer_relations(entity_id: str, entity_type: str, direction: str, relation_type: str) -> dict:
        try:
            entity_query = {
                "entityFields": [
                    {
                        "type": "ENTITY_FIELD",
                        "key": "name"
                    },
                    {
                        "type": "ENTITY_FIELD",
                        "key": "id"
                    },
                    {
                        "type": "ENTITY_FIELD",
                        "key": "type"
                    },
                    {
                        "type": "ENTITY_FIELD",
                        "key": "label"
                    },
                ],
                "latestValues": [],
                "entityFilter": {
                    "type": "relationsQuery",
                    "rootEntity": {
                        "entityType": entity_type,
                        "id": entity_id
                    },
                    "direction": direction,
                    "fetchLastLevelOnly": False,
                    "filters": [
                        {
                            "relationType": relation_type,
                            "entityTypes": ["CUSTOMER"]
                        }
                    ]
                },
                "pageLink": {
                    "dynamic": True, "page": 0, "pageSize": 100,
                    "sortOrder": {
                        "direction": "ASC"
                    },
                    "textSearch": ""
                }
            }
            return entity_query
        except Exception as e:
            logger.exception("can't create a relation query - %s" % str(e))

    @staticmethod
    def customer_construct(data: dict, customer_id: str, customer_type: str) -> list:
        customer = [{"customerName": data.get("name"),
                     "customerId": customer_id,
                     "customerType": customer_type}]
        return customer

    @staticmethod
    def tenant_construct(data: dict, tenant_id: str, tenant_type: str) -> list:
        tenant = [{"customerName": data.get("name"),
                   "customerId": tenant_id,
                   "customerType": tenant_type}]
        return tenant

    @staticmethod
    def wattage_construct(data: list, lamp_profiles: list) -> list:
        recommend_watt = []
        for lamp_prof in lamp_profiles:
            for watt in data:
                recommend_watt.append({"type": "LED",
                                       "recommended_wattage": watt.get("value").get(lamp_prof.get("type")).get(
                                           str(lamp_prof.get("watts")))})
        return recommend_watt


class AttendanceService(TbEntityController):
    def handle_login(self, _username, _password, request_handler, _ts, latitude, longitude):
        try:
            auth_service = TbAuthService(instance=request_handler)
            _token = auth_service.login(user_name=_username, password=_password)
            if _token.get("token"):
                user_info = auth_service.get_user_details(header=_token.get("token"))
                _id = user_info.get("id").get("id")
                asset_service = AssetService(token=_token.get("token"), instance=request_handler)
                _data = asset_service.get_entity_attribute(
                    entity_type="USER",
                    entity_id=_id,
                    scope=ATTRIBUTE_SCOPES[1],
                    keys="isAttendanceCaptureRequired,isLocationTrackingRequired,isPPERequired,isCheckedIn,employeeId,locationTrackingInterval")
                _result = {item["key"]: item["value"] for item in _data}
                _result["token"] = _token.get("token")
                _result["refreshToken"] = _token.get("refreshToken")
                request_handler.update_service_response(_result)
                _response = json.loads(request_handler.get_service_response())
                telemetry_data = {}
                if _response.get("isLocationTrackingRequired") and _response.get("isLocationTrackingRequired") == True:
                    telemetry_data["latitude"] = latitude
                    telemetry_data["longitude"] = longitude
                if _response.get("isAttendanceCaptureRequired") and _response.get("isAttendanceCaptureRequired") == True:
                    employee_swipe = self.attendance_swipes(timestamp=_ts, employee_id=_response.get("employeeId"), status=1)
                    if employee_swipe:
                        asset_service.update_entity_attribute(
                            entity_type="USER",
                            entity_id=_id,
                            scope=ATTRIBUTE_SCOPES[1],
                            data=json.dumps({"isCheckedIn":True})
                        )
                        telemetry_data["activity"] = "Check-In"
                        logger.info("Attendance: Check-In successful for the user %s" % str(_username))
                    else:
                        logger.exception("Attendance: Check-In failed due to no swipe found ")
                        asset_service.update_entity_attribute(
                            entity_type="USER",
                            entity_id=_id,
                            scope=ATTRIBUTE_SCOPES[1],
                            data=json.dumps({"isCheckedIn": False})
                        )
                if telemetry_data:
                    asset_service.update_telemetry(
                        entity_type="USER",
                        entity_id=_id,
                        data=telemetry_data)
                request_handler.update_service_response(_result)
            else:
                logger.exception("Login failed due to no token found")
        except Exception as e:
            logger.exception("Login failed due to - %s" % str(e))


    def handle_logout(self, _token, request_handler, _username, _ts, latitude, longitude):
        try:
            auth_service = TbAuthService(instance=request_handler)
            asset_service = AssetService(token=_token, instance=request_handler)
            user_info = auth_service.get_user_details(header=_token)
            _id = user_info.get("id").get("id")
            _data = asset_service.get_entity_attribute(
                entity_type="USER",
                entity_id=_id,
                scope=ATTRIBUTE_SCOPES[1],
                keys="isAttendanceCaptureRequired,employeeId")
            _result = {item["key"]: item["value"] for item in _data}
            telemetry_data = {"latitude": latitude, "longitude": longitude}
            if "isAttendanceCaptureRequired" in _result and _result.get("isAttendanceCaptureRequired") == True and "employeeId" in _result:
                employee_swipe = self.attendance_swipes(timestamp=_ts, employee_id=_result.get("employeeId"), status=0)
                if employee_swipe:
                    asset_service.update_entity_attribute(
                        entity_type="USER",
                        entity_id=_id,
                        scope=ATTRIBUTE_SCOPES[1],
                        data=json.dumps({"isCheckedIn": False})
                    )
                    telemetry_data["activity"] = "Check-Out"
                    logger.info("Attendance: Check-out successful for the user %s" % str(_username))
            asset_service.update_telemetry(
                entity_type="USER",
                entity_id=_id,
                data=telemetry_data)
            auth_service.logout(header=_token)
            logger.info("User Log out Successful: %s" % _username)
            request_handler.update_service_response({"status": 200})
        except Exception as e:
            logger.exception("Logout failed due to - %s" % str(e))


    def attendance_swipes(self, timestamp, employee_id, status):
        try:
            ist  = timezone(timedelta(hours=5, minutes=30))
            if isinstance(timestamp, str):
                timestamp = int(timestamp)
            timestamp = timestamp / 1000

            times = datetime.fromtimestamp(timestamp, ist).isoformat(timespec='milliseconds')
            door_name = "Luminator App"
            swipes = f"{times},{employee_id},{door_name},{status}"
            key = RSA.importKey(open(GREYTHR_PEM_FILEPATH).read())
            h = SHA1.new(swipes.encode('utf-8'))
            signer = PKCS1_v1_5.new(key)
            signature = signer.sign(h)
            attendance_sign = base64.b64encode(signature).decode()
            swipes = urllib.parse.quote(swipes)
            updated_attendance_sign = urllib.parse.quote(attendance_sign)
            conn = http.client.HTTPSConnection(GREYTHR_HOST)
            payload = 'id=' + GREYTHR_API_ID + '&swipes=' + swipes + '&sign=' + updated_attendance_sign
            headers = {'X-Requested-With': 'XMLHttpRequest', 'Content-Type': 'application/x-www-form-urlencoded'}
            conn.request("POST", GREYTHR_API_ENDPOINT, payload, headers)
            hr_response = conn.getresponse().read().decode("utf-8")
            if hr_response != '':
                logger.exception("Server responded with error:" + hr_response)
                return False
            else:
                logger.info("Attendance: Swipes uploaded successfully")
            return True
        except Exception as e:
            logger.exception("Error occurred while uploading swipes :" + str(e))
            return False

    @staticmethod
    def publish_iam_user_data_to_pubsub(message):
        publisher = pubsub_v1.PublisherClient.from_service_account_json(SERVICE_ACCOUNT_KEY_PATH)
        # Publish the message to the specified topic
        message_future = publisher.publish(PUBSUB_IAM_USER_DATA_TOPIC_PATH, data=json.dumps(message).encode('utf-8'))
        logger.info(f"Published message {message_future.result()} to {PUBSUB_IAM_USER_DATA_TOPIC_PATH}")


class EbMeterService:
    def replace_ebmeter(self, request_handler, asset_service, request_params):
        try:
            _ccms_id = request_params.get("ccmsId")
            _new_eb_meter_data = request_params.get("replaceWith")
            _new_ebmeter = _new_eb_meter_data.get("ebmeterNo")
            _attr_data = {"ebMeterNo": _new_ebmeter}
            _telemetry_data = {"ts": _new_eb_meter_data.get("installedOn"), "values": {
                "ebMeterNo": _new_ebmeter,
                "ebMeterReplacedOn": _new_eb_meter_data.get("installedOn"),
                "ebMeterReplacedBy": _new_eb_meter_data.get("installedBy"),
                "ebmeterAuditImg": _new_eb_meter_data.get("auditImg"),
                "newMeterReading": _new_eb_meter_data.get("newMeterReading"),
                "oldMeterReading": _new_eb_meter_data.get("oldMeterReading")
            }}
            # Updating telemetry
            asset_service.update_telemetry(entity_type=ENTITY_TYPE[1], entity_id=_ccms_id, data=_telemetry_data)
            # Updating attribute
            asset_service.update_entity_attribute(
                entity_type=ENTITY_TYPE[1], entity_id=_ccms_id, scope=ATTRIBUTE_SCOPES[1], data=json.dumps(_attr_data))
            # construct message for pubsub of old ebmeter
            _message_old = asset_service.construct_pubsub_ebmeter_acitivty_log(
                request_params, activity="removed", ebmeterNo=request_params.get("ebmeterNo"))
            asset_service.publish_activity_log_to_pubsub(
                message=_message_old, topic_path=PUBSUB_ACTIVITY_LOG_TOPIC_PATH)
            # construct message for pubsub of new ebmeter
            _message_new = asset_service.construct_pubsub_ebmeter_acitivty_log(
                request_params, activity="installed", ebmeterNo=_new_ebmeter)
            asset_service.publish_activity_log_to_pubsub(
                message=_message_new, topic_path=PUBSUB_ACTIVITY_LOG_TOPIC_PATH)
        except Exception as e:
            request_handler.update_error_response(str(e))
            logger.exception("eb meter not replaced  %s" % str(e))


class CommissionService:
    @staticmethod
    def update_mapped_lamb_details(ccms_name, ccms_id, asset_service):
        try:
            _payload = {
                "entityFilter": {"type": "assetType", "assetType": "lightPoint"},
                "keyFilters": [
                    {"key": {"type": "ATTRIBUTE", "key": "poweredBy"},
                     "valueType": "STRING",
                     "predicate": {"operation": "EQUAL", "value": {"defaultValue": ccms_name, "dynamicValue": None},
                                   "type": "STRING"}
                     }
                ],
                "entityFields": [{"type": "ENTITY_FIELD", "key": "name"}, {"type": "ENTITY_FIELD", "key": "label"}],
                "latestValues": [{"type": "ATTRIBUTE", "key": "lampWatts"}],
                "pageLink": {"page": 0, "pageSize": 100,
                             "sortOrder": {"key": {"key": "name", "type": "ENTITY_FIELD"}, "direction": "ASC"}}
            }
            _data = asset_service.search_entity_query(data=json.dumps(_payload))
            _attribute_data = []
            for _item in _data["data"]:
                _lamp_watt = _item["latest"]["ATTRIBUTE"]["lampWatts"]["value"]
                if not _lamp_watt:
                    continue
                _lamp_watt = int(_lamp_watt)
                for _entry in _attribute_data:
                    if _entry["watts"] == _lamp_watt:
                        _entry["count"] += 1
                        break
                else:
                    _attribute_data.append({"watts": _lamp_watt, "count": 1})
            _tel_value = {"ts": int(time.time()) * 1000, "values": {"mappedLampDetail": _attribute_data}}
            asset_service.update_telemetry(entity_type=ENTITY_TYPE[1], entity_id=ccms_id, data=_tel_value)
        except Exception as e:
            logger.exception("couldn't update mappedLampDetails - %s" % str(e))
