-- Database initialization script for Schnelliot FastAPI
-- This creates the necessary tables for device onboarding

CREATE DATABASE IF NOT EXISTS schnelliot_db;
USE schnelliot_db;

-- Device master table for ILM devices
CREATE TABLE IF NOT EXISTS device_master (
    did INT AUTO_INCREMENT PRIMARY KEY,
    device_no VARCHAR(50) UNIQUE,
    ieee_address VARCHAR(50) UNIQUE NOT NULL,
    tb_id VARCHAR(100) DEFAULT '-',
    added_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_ieee_address (ieee_address),
    INDEX idx_device_no (device_no),
    INDEX idx_tb_id (tb_id)
);

-- Gateway device master table
CREATE TABLE IF NOT EXISTS gateway_device_master (
    gid INT AUTO_INCREMENT PRIMARY KEY,
    board_no VARCHAR(50) UNIQUE,
    imei_no VARCHAR(50) UNIQUE NOT NULL,
    tb_production_id VARCHAR(100) DEFAULT '-',
    datetime DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_imei_no (imei_no),
    INDEX idx_board_no (board_no),
    INDEX idx_tb_production_id (tb_production_id)
);

-- Insert some sample data for testing
INSERT IGNORE INTO device_master (device_no, ieee_address, tb_id) VALUES 
('ZAA001', '1234567890123456', 'sample-tb-id-1'),
('ZAA002', '1234567890123457', 'sample-tb-id-2');

INSERT IGNORE INTO gateway_device_master (board_no, imei_no, tb_production_id) VALUES 
('#AA001', '123456789012345', 'sample-gw-tb-id-1'),
('#AA002', '123456789012346', 'sample-gw-tb-id-2');

-- Create user for the application
CREATE USER IF NOT EXISTS 'schnelliot'@'%' IDENTIFIED BY 'schnelliot123';
GRANT ALL PRIVILEGES ON schnelliot_db.* TO 'schnelliot'@'%';
FLUSH PRIVILEGES;
