import datetime
# for thingsboard
import json

import pymysql
import requests
import simplejson
from django.http import HttpResponse

from onboarder import tb_url, logger
from onboarder.service import TBConnection
from tb_service.settings import DATABASES, LOGIN_CREDENTIALS

# ILM_VARIANTS_PROFILE_MAPPING contains ilm variants with corresponding profile and its credentials length
ILM_VARIANTS_PROFILE_MAPPING = {
    "SLNNSGINSTDGX01A": ("ilm", 16),
    "SLNN4GQTLSTDG01A": ("ilm-4g", 15),
    "SLNN4GQTLDM0701A": ("ilm-4g", 15),
    "PILCNSGINPROG01A": ("ilm", 16),
}


dev_no_prefix = {0: 'AA', 1: 'AB', 2: 'AC', 3: 'AD', 4: 'AE', 5: 'AF', 6: 'AG', 7: 'AH', 8: 'AI', 9: 'AJ',
                 10: 'AK', 11: 'AL', 12: 'AM', 13: 'AN', 14: 'AO', 15: 'AP', 16: 'AQ', 17: 'AR', 18: 'AS',
                 19: 'AT', 20: 'AU', 21: 'AV', 22: 'AW', 23: 'AX', 24: 'AY', 25: 'AZ', 26: 'BA', 27: 'BB',
                 28: 'BC', 29: 'BD', 30: 'BE', 31: 'BF', 32: 'BG', 33: 'BH', 34: 'BI', 35: 'BJ', 36: 'BK',
                 37: 'BL', 38: 'BM', 39: 'BN', 40: 'BO', 41: 'BP', 42: 'BQ', 43: 'BR', 44: 'BS', 45: 'BT',
                 46: 'BU', 47: 'BV', 48: 'BW', 49: 'BX', 50: 'BY', 51: 'BZ', 52: 'CA', 53: 'CB', 54: 'CC',
                 55: 'CD', 56: 'CE', 57: 'CF', 58: 'CG', 59: 'CH', 60: 'CI', 61: 'CJ', 62: 'CK', 63: 'CL',
                 64: 'CM', 65: 'CN', 66: 'CO', 67: 'CP', 68: 'CQ', 69: 'CR', 70: 'CS', 71: 'CT', 72: 'CU',
                 73: 'CV', 74: 'CW', 75: 'CX', 76: 'CY', 77: 'CZ', 78: 'DA', 79: 'DB', 80: 'DC', 81: 'DD',
                 82: 'DE', 83: 'DF', 84: 'DG', 85: 'DH', 86: 'DI', 87: 'DJ', 88: 'DK', 89: 'DL', 90: 'DM',
                 91: 'DN', 92: 'DO', 93: 'DP', 94: 'DQ', 95: 'DR', 96: 'DS', 97: 'DT', 98: 'DU', 99: 'DV',
                 100: 'DW'}

tb_user_data = LOGIN_CREDENTIALS

db_config = {
    'host': DATABASES["default"]["HOST"],  # Usually 'localhost' for local development
    'user': DATABASES["default"]["USER"],
    'password': DATABASES["default"]["PASSWORD"],
    'db': DATABASES["default"]["NAME"],
    'port': int(DATABASES["default"]["PORT"]),
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor,  # Return query results as dictionaries
}


def add_ilm_device(request, credentials, variant):
    new_device_no = ""
    device_profile_map = ILM_VARIANTS_PROFILE_MAPPING.get(variant, 'ilm')
    if len(credentials) == device_profile_map[1]:
        try:
            conn = pymysql.connect(**db_config)
            cur = conn.cursor()
            isql = "select device_no, ieee_address, tb_id from device_master where ieee_address = '" + str(
                credentials) + "' and tb_id <> '-'"
            cur.execute(isql)
            device_data = cur.fetchall()
            conn.close()
            if len(device_data) >= 1:
                try:
                    device_no = device_data[0]['device_no']
                    return HttpResponse(content=simplejson.dumps(device_no), content_type='application/json')
                except Exception as e:
                    logger.exception("add_node_device 4008  : %s", str(e))
                    response = {'error_code': '4008'}
                    return HttpResponse(content=simplejson.dumps(response), content_type='application/json')
            else:
                conn = pymysql.connect(**db_config)
                cur = conn.cursor()
                isql = "select device_no, ieee_address, tb_id from device_master where ieee_address = '" + str(
                    credentials) + "'"
                cur.execute(isql)
                device_no = cur.fetchall()
                if len(device_no) > 0:
                    new_device_no = device_no[0]['device_no']
                else:
                    isql = "insert into device_master (ieee_address, added_date) values ('%s', '%s') " % \
                           (credentials, str(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')))
                    cur.execute(isql)
                    conn.commit()
                    isql = "select * from device_master where ieee_address = '" + str(credentials) + "'"
                    cur.execute(isql)
                    did = cur.fetchall()
                    dev_no = did[0]['did']
                    dev_index_no = int(int(dev_no) / 1000)
                    if dev_index_no:
                        # Project ID 'Z' + AA001
                        new_device_no = 'Z' + str(dev_no_prefix[dev_index_no]) + str(
                            dev_no - (1000 * dev_index_no)).zfill(
                            3)
                    else:
                        new_device_no = 'Z' + str(dev_no_prefix[dev_index_no]) + str(dev_no).zfill(3)
                    isql = "update device_master set device_no = '" + str(new_device_no) + "' where ieee_address = '" \
                           + str(credentials) + "'"
                    cur.execute(isql)
                    conn.commit()
                conn.close()
                # login to get token
                tb_auth_token = TBConnection.get_tb_token(request, tb_user_data)
                header = {'Content-type': 'application/json', 'Accept': '*/*',
                          'X-Authorization': 'Bearer ' + tb_auth_token.get('token')}
                # get entity group id for device group
                try:
                    data = ''
                    tb_api_url = tb_url + "/entityGroups/DEVICE"
                    tb_get_entitygroup_response = requests.get(tb_api_url, headers=header, data='', timeout=60)
                    if tb_get_entitygroup_response.text.find('Token has expired') != -1:
                        tb_get_entitygroup_response = TBConnection.refresh_tb_token(request, tb_api_url, 'get', tb_url,
                                                                                    tb_user_data, data)
                        header = {'Content-type': 'application/json', 'Accept': '*/*',
                                  'X-Authorization': 'Bearer ' + request.session['tb_token']}
                    tb_entitygroup_json_data = json.loads(tb_get_entitygroup_response.text)
                    for item in tb_entitygroup_json_data:
                        if str(item.get('name')).find('All') != -1:
                            tb_entitygroup_id = item.get('id').get('id')
                            break
                    logger.info("Entitygroup_id : %s", str(tb_entitygroup_id))
                except Exception as e:
                    logger.exception("add_node_device 4002 : %s", str(e))
                    response = {'error_code': '4002'}
                    return HttpResponse(content=simplejson.dumps(response), content_type='application/json')
                # add device using token
                try:
                    apiurl1 = tb_url + "/device"
                    data = {"name": str(new_device_no), "type": device_profile_map[0]}
                    tb_get_device_response = requests.post(apiurl1, headers=header, data=json.dumps(data), timeout=60)
                    if tb_get_device_response.text.find('Token has expired') != -1:
                        tb_get_device_response = TBConnection.refresh_tb_token(request, tb_api_url, 'post', tb_url,
                                                                               tb_user_data, data)
                        header = {'Content-type': 'application/json', 'Accept': '*/*',
                                  'X-Authorization': 'Bearer ' + request.session['tb_token']}
                    tb_device_json_data = json.loads(tb_get_device_response.text)
                    tb_device_id = tb_device_json_data.get('id').get('id')
                except Exception as e:
                    logger.exception("add_node_device 4003 : %s", str(e))
                    response = {'error_code': '4003'}
                    return HttpResponse(content=simplejson.dumps(response), content_type='application/json')
                # get credentials for added device
                try:
                    apiurl1 = tb_url + "/device/" + tb_device_id + "/credentials"
                    data = ''
                    tb_get_device_credentials_response = requests.get(apiurl1, headers=header, data=data, timeout=60)
                    if tb_get_device_credentials_response.text.find('Token has expired') != -1:
                        tb_get_device_credentials_response = TBConnection.refresh_tb_token(request, tb_api_url, 'get',
                                                                                           tb_url, tb_user_data, data)
                        header = {'Content-type': 'application/json', 'Accept': '*/*',
                                  'X-Authorization': 'Bearer ' + request.session['tb_token']}
                    tb_device_credentials_json_data = json.loads(tb_get_device_credentials_response.text)
                except Exception as e:
                    logger.exception("add_node_device 4004 : %s", str(e))
                    response = {'error_code': '4004'}
                    return HttpResponse(content=simplejson.dumps(response), content_type='application/json')
                # change credentials for added device
                try:
                    tb_device_credentials_json_data['credentialsId'] = credentials
                    tb_device_credentials_json_data['credentialsValue'] = 'null'
                    apiurl1 = tb_url + "/device/credentials"
                    data = str(tb_device_credentials_json_data).replace("u'", "'").replace("'", '"')
                    tb_change_device_credentials_response = requests.post(apiurl1, headers=header, data=data,
                                                                          timeout=60)
                    if tb_change_device_credentials_response.text.find('Token has expired') != -1:
                        tb_change_device_credentials_response = TBConnection.refresh_tb_token(request, tb_api_url,
                                                                                              'post',
                                                                                              tb_url, tb_user_data,
                                                                                              data)
                        header = {'Content-type': 'application/json', 'Accept': '*/*',
                                  'X-Authorization': 'Bearer ' + request.session['tb_token']}
                    tb_change_device_credentials_json_data = json.loads(tb_change_device_credentials_response.text)
                except Exception as e:
                    logger.exception("add_node_device 4004a : %s", str(e))
                    response = {'error_code': '4004'}
                    return HttpResponse(content=simplejson.dumps(response), content_type='application/json')
                # add server attributes
                try:
                    apiurl1 = tb_url + "/plugins/telemetry/DEVICE/" + tb_device_id + \
                              "/SERVER_SCOPE"
                    data = '{"inactivityTimeout": 1000000, "testResults":{ "brightness_100": false,' \
                           '"brightness_70": false, ' \
                           '"brightness_50": false, "brightness_30": false,' \
                           '"brightness_0": false, "flash": false, ' \
                           '"rtc": false, "error_count":0},"dimmable":true,' \
                           '"state":"ONBOARDED","condition":"NEW","qrCount":0,' \
                           '"boardNumber":"' + new_device_no + '", "variant":"' + variant + '"}'
                    tb_add_attributes_response = requests.post(apiurl1, headers=header, data=data, timeout=60)
                    if tb_add_attributes_response.text.find('Token has expired') != -1:
                        tb_add_attributes_response = TBConnection.refresh_tb_token(request, tb_api_url, 'post', tb_url,
                                                                                   tb_user_data, data)
                        header = {'Content-type': 'application/json', 'Accept': '*/*',
                                  'X-Authorization': 'Bearer ' + request.session['tb_token']}
                except Exception as e:
                    logger.exception("add_node_device 4005 : %s", str(e))
                    response = {'error_code': '4005'}
                    return HttpResponse(content=simplejson.dumps(response), content_type='application/json')
                try:
                    conn = pymysql.connect(**db_config)
                    cur = conn.cursor()
                    isql = "update device_master set tb_id = '" + str(tb_device_id) + "' where ieee_address = '" + str(
                        credentials) + "'"
                    cur.execute(isql)
                    conn.commit()
                    conn.close()
                except Exception as e:
                    logger.exception("add_node_device 4006 : %s", str(e))
                    response = {'error_code': '4006'}
                    return HttpResponse(content=simplejson.dumps(response), content_type='application/json')
        except Exception as e:
            logger.exception("add_device --> %s", str(e))
        logger.info("new_device_no -->  %s", str(new_device_no))
    else:
        new_device_no = "Please enter the valid IEEE Address and variant"
        logger.info("Please enter the valid IEEE Address")
    return HttpResponse(content=simplejson.dumps(new_device_no), content_type='application/json')


def add_gateway_device(request, imei_no, variant):
    new_device_no = ""
    try:
        conn = pymysql.connect(**db_config)
        cur = conn.cursor()
        isql = "select board_no, imei_no, tb_production_id from gateway_device_master where imei_no = '" + str(
            imei_no) + "' and tb_production_id <> '-'"
        cur.execute(isql)
        device_no = cur.fetchall()
        conn.close()
        if len(device_no) >= 1:
            try:
                return HttpResponse(content=simplejson.dumps(device_no[0]['board_no']), content_type='application/json')
            except Exception as e:
                response = {'error_code': '4008'}
                logger.exception('add_gateway_device 4008 --> %s', str(e))
                return HttpResponse(content=simplejson.dumps(response), content_type='application/json')
        else:
            conn = pymysql.connect(**db_config)
            cur = conn.cursor()
            isql = "select board_no from gateway_device_master where imei_no = '" + str(imei_no) + "'"
            cur.execute(isql)
            dev_data = cur.fetchall()
            if len(dev_data) > 0:
                new_device_no = dev_data[0]['board_no']
            else:
                isql = "insert into gateway_device_master (imei_no, datetime) values ('%s', '%s') " % \
                       (imei_no, str(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')))
                cur.execute(isql)
                conn.commit()
                isql = "select * from gateway_device_master where imei_no = '" + str(imei_no) + "'"
                cur.execute(isql)
                did = cur.fetchall()
                dev_no = did[0]['gid']
                dev_index_no = int(int(dev_no) / 1000)
                if dev_index_no:
                    new_device_no = '#' + str(dev_no_prefix[dev_index_no]) + str(dev_no - (1000 * dev_index_no)).zfill(
                        3)
                else:
                    new_device_no = '#' + str(dev_no_prefix[dev_index_no]) + str(dev_no).zfill(3)
                isql = "update gateway_device_master set board_no = '" + str(new_device_no) + "' where imei_no = '" \
                       + str(imei_no) + "'"
                cur.execute(isql)
                conn.commit()
            conn.close()
            # Refresh the token
            # tb_auth_token = TBConnection.refresh_tb_token(request, tb_user_data)
            tb_auth_token = TBConnection.get_tb_token(request, tb_user_data)
            header = {'Content-type': 'application/json', 'Accept': '*/*',
                      'X-Authorization': 'Bearer ' + tb_auth_token.get('token')}
            # get entity group id for device group
            try:
                tb_api_url = tb_url + "/entityGroups/DEVICE"
                tb_get_entitygroup_response = requests.get(tb_api_url, headers=header, data='', timeout=60)
                data = ''
                if tb_get_entitygroup_response.text.find('Token has expired') != -1:
                    tb_get_entitygroup_response = TBConnection.refresh_tb_token(request, tb_api_url, 'get', tb_url,
                                                                                tb_user_data, data)
                    header = {'Content-type': 'application/json', 'Accept': '*/*',
                              'X-Authorization': 'Bearer ' + request.session['tb_token']}
                tb_entitygroup_json_data = json.loads(tb_get_entitygroup_response.text)
                for item in tb_entitygroup_json_data:
                    if str(item.get('name')).find('All') != -1:
                        tb_entitygroup_id = item.get('id').get('id')
                        break
            except Exception as e:
                response = {'error_code': '4002'}
                logger.exception('add_gateway_device 4002 --> %s', str(e))
                return HttpResponse(content=simplejson.dumps(response), content_type='application/json')
            # add device using token
            try:
                tb_api_url = tb_url + "/device?entityGroupId=" + str(tb_entitygroup_id)
                data = '{"name":"' + str(new_device_no) + '","type":"gw"}'
                tb_get_device_response = requests.post(tb_api_url, headers=header, data=data, timeout=60)
                if tb_get_device_response.text.find('Token has expired') != -1:
                    tb_get_device_response = TBConnection.refresh_tb_token(request, tb_api_url, 'post', tb_url,
                                                                           tb_user_data, data)
                    header = {'Content-type': 'application/json', 'Accept': '*/*',
                              'X-Authorization': 'Bearer ' + request.session['tb_token']}
                tb_device_json_data = json.loads(tb_get_device_response.text)
                tb_device_id = tb_device_json_data.get('id').get('id')
                logger.info("Device_id: %s", str(tb_device_id))
            except Exception as e:
                response = {'add_gateway_device error_code': '4003'}
                logger.exception('add_gateway_device 4003 --> %s', str(e))
                return HttpResponse(content=simplejson.dumps(response), content_type='application/json')
            # get credentials for added device
            try:
                apiurl1 = tb_url + "/device/" + tb_device_id + "/credentials"
                data = ''
                tb_get_device_credentials_response = requests.get(apiurl1, headers=header, data=data, timeout=60)
                if tb_get_device_credentials_response.text.find('Token has expired') != -1:
                    tb_get_device_credentials_response = TBConnection.refresh_tb_token(request, tb_api_url, 'get',
                                                                                       tb_url, tb_user_data, data)
                    header = {'Content-type': 'application/json', 'Accept': '*/*',
                              'X-Authorization': 'Bearer ' + request.session['tb_token']}
                tb_device_credentials_json_data = json.loads(tb_get_device_credentials_response.text)
            except Exception as e:
                response = {'error_code': '4004'}
                logger.exception('add_gateway_device 4004 --> %s', str(e))
                return HttpResponse(content=simplejson.dumps(response), content_type='application/json')
            # change credentials for added device
            try:
                tb_device_credentials_json_data['credentialsId'] = imei_no
                tb_device_credentials_json_data['credentialsValue'] = 'null'
                apiurl1 = tb_url + "/device/credentials"
                data = str(tb_device_credentials_json_data).replace("u'", "'").replace("'", '"')
                tb_change_device_credentials_response = requests.post(apiurl1, headers=header, data=data, timeout=60)
                if tb_change_device_credentials_response.text.find('Token has expired') != -1:
                    tb_change_device_credentials_response = TBConnection.refresh_tb_token(request, tb_api_url, 'post',
                                                                                          tb_url, tb_user_data, data)
                    header = {'Content-type': 'application/json', 'Accept': '*/*',
                              'X-Authorization': 'Bearer ' + request.session['tb_token']}
                tb_change_device_credentials_json_data = json.loads(tb_change_device_credentials_response.text)
            except Exception as e:
                response = {'error_code': '4004'}
                logger.exception('add_gateway_device 40041 --> %s', str(e))
                return HttpResponse(content=simplejson.dumps(response), content_type='application/json')
            # add server attributes
            try:
                logger.info('server atrribute updating')
                apiurl1 = tb_url + "/plugins/telemetry/DEVICE/" + tb_device_id + "/SERVER_SCOPE"
                data = '{"state": "ONBOARDED", "condition": "NEW", "inactivityTimeout": 2100000,' \
                       '"boardNumber":"' + new_device_no + '","variant":"' + variant + '"}'
                tb_add_attributes_response = requests.post(apiurl1, headers=header, data=data, timeout=60)
                logger.info('server_attribute_insert: %s', str(tb_add_attributes_response.text))
                if tb_add_attributes_response.text.find('Token has expired') != -1:
                    tb_add_attributes_response = TBConnection.refresh_tb_token(request, tb_api_url, 'post', tb_url,
                                                                               tb_user_data, data)
            except Exception as e:
                response = {'error_code': '4005'}
                logger.exception('add_gateway_device 4005 --> %s', str(e))
                return HttpResponse(content=simplejson.dumps(response), content_type='application/json')
            try:
                conn = pymysql.connect(**db_config)
                cur = conn.cursor()
                isql = "update gateway_device_master set tb_production_id = '" + str(
                    tb_device_id) + "' ""where imei_no = '" + str(imei_no) + "'"
                cur.execute(isql)
                conn.commit()
                conn.close()
            except Exception as e:
                response = {'error_code': '4006'}
                logger.exception('add_gateway_device 4006 --> %s', str(e))
                return HttpResponse(content=simplejson.dumps(response), content_type='application/json')
    except Exception as e:
        logger.exception("add_gateway_device 4009 --> %s", str(e))
    return HttpResponse(content=simplejson.dumps(new_device_no), content_type='application/json')
