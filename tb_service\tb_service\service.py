from abc import abstractmethod, ABC


class TbAuthController(ABC):
    login_url: str = "/api/auth/login"
    logout_url: str = "/api/auth/logout"
    get_all_permissions_url = "/api/permissions/allowedPermissions"
    header: dict = {'content-type': 'application/json'}
    login_response = {"token": "", "refresh_token": ""}
    get_role_url = "/api/roles?pageSize=1000&page=0"
    get_user_details_url = "/api/auth/user"
    get_user_info_by_id_url = "/api/user/info/{user_id}"

    @abstractmethod
    def login(self, user_name: str, password: str) -> str:
        pass

    @abstractmethod
    def logout(self, header: str) -> None:
        pass

    @abstractmethod
    def get_user_permissions(self, header: str) -> bool:
        pass


class TbEntityController(ABC):
    ENTITY_GROUP_ADD_ENTITIES = "/api/entityGroup/{entity_group_id}/addEntities"
    ENTITY_GROUP_DELETE_ENTITIES = "/api/entityGroup/{entity_group_id}/deleteEntities"
    get_entity_groupId_url = "/api/entityGroup/all/{owner_type}/{owner_id}/{group_type}"
    get_entity_groupId_from_owner_url = "/api/entityGroup/{owner_type}/{owner_id}/{group_type}/{group_name}"
    get_entity_groupId_by_entityId_url = "/api/entityGroups/{entity_type}/{entity_id}"

    entity_by_id_url = "/api/{entity_type}/{entity_id}"
    entity_by_name_url = "/api/tenant/{entity_type}s?{query_name}={entity_name}"
    get_entity_info_by_id_url = "/api/{entity_type}/info/{entity_id}"
    entity_attribute_url = "/api/plugins/telemetry/{entity_type}/{entity_id}/values/attributes/{scope}?keys={keys}"
    update_entity_attribute_url = "/api/plugins/telemetry/{entity_type}/{entity_id}/attributes/{scope}"
    delete_entity_attribute_url = "/api/plugins/telemetry/{entity_type}/{entity_id}/{scope}?keys={keys}"
    create_relation = "/api/relation"
    get_to_relation_info = "/api/relations/info?toId={to_id}&toType={to_type}"
    remove_relation = "/api/relation?fromId={from_id}&fromType={from_type}&" \
                      "relationType={relation_type}&relationTypeGroup=COMMON&toId={to_id}&toType={to_type}"
    create_or_update_entity_url = "/api/{entity_type}"
    get_all_regions_url = "/api/user/{entity_type}s?pageSize={pagesize}&page={page}&type={asset_type}"
    get_all_entities_url = "/api/relations?fromId={from_id}&fromType={from_type}"
    entity_query_find_url = "/api/entitiesQuery/find"
    entity_search_url = "/api/user/{entity_type}s?pageSize={pagesize}&page={page}&textSearch={textsearch}"
    on_off_control_url = "/api/plugins/rpc/oneway/{device_id}"
    remove_all_relation_url = "/api/relations?entityId={entity_id}&entityType={entity_type}"
    get_from_relation_info_url = "/api/relations/info?fromId={from_id}&fromType={from_type}"
    change_ownership_url = "/api/owner/{owner_type}/{owner_id}/{entity_type}/{entity_id}"

    update_telemetry_url = "/api/plugins/telemetry/{entity_type}/{entity_id}/timeseries/{scope}"
    get_entity_to_relation_url = "/api/relations?toId={entity_id}&toType={entity_type}&relationType={relation_type}"
    get_relation_info_fromid_url = "/api/relations?fromId={from_id}&fromType={from_type}&relationType={relation_type}"
    get_relation_info_toid_url = "/api/relations?toId={to_id}&toType={to_type}&relationType={relation_type}"
    get_telemetry_data_url = "/api/plugins/telemetry/{entity_type}/{entity_id}/values/timeseries?keys={key}&useStrictDataTypes=false"
    search_entity_query_url = "/api/entitiesQuery/find"
    get_user_by_name_url = "/api/user/users?pageSize=100&page=0&textSearch={user_name}"

    ilm_rpc = {"on": {"method": "ctrl", "params": {"rpcType": 4, "mode": 3, "lamp": 1}},
               "off": {"method": "ctrl", "params": {"rpcType": 4, "mode": 3, "lamp": 0}},
               "getlive": {"method": "get", "params": {"rpcType": 1, "value": 0}}}

    gw_rpc = {"on": {"method": "ctrl", "params": {"lamp": 1, "mode": 2}},
              "off": {"method": "ctrl", "params": {"lamp": 0, "mode": 2}},
              "mcbon": {"method": "set", "params": {'rostat': 0, 'yostat': 0, 'bostat': 0}},
              "mcbclear": {"method": "clr", "params": 5},
              "getlive": {"method": "get", "params": 0}}

    def get_entity_by_id(self, entity_type: str, entity_id: str) -> dict:
        pass

    def get_entity_by_name(self, entity_type: str, name: str) -> dict:
        pass

    def get_entity_attribute(self, entity_type: str, entity_id: str, scope: str, keys: str) -> list:
        pass

    def update_entity_attribute(self, entity_type: str, entity_id: str, scope: str, data: str) -> None:
        pass

    def delete_entity_attribute(self, entity_type: str, entity_id: str, scope: str, keys: str) -> None:
        pass

    def get_entity_to_relation_info(self, to_id: str, to_type: str) -> list:
        pass

    def create_entity_relation(self, from_id: str, from_type: str, to_id: str, to_type: str,
                               relation_type: str) -> dict:
        pass

    def remove_entity_relation(self, from_id: str, from_type: str, to_id: str, to_type: str,
                               relation_type: str) -> None:
        pass

    def create_or_update_entity(self, entity_type: str, data: dict) -> dict:
        pass


class TbCustomerController(ABC):
    get_owner_by_id_url = "/api/{owner_type}/{owner_id}"
    get_all_customers_url = "/api/user/customers?pageSize=1000&page=0"
    get_all_customer_assets_url = "/api/customer/{customer_id}/assets?pageSize={page_size}&page={page}&type={asset_type}"

    @abstractmethod
    def get_customer_by_id(self, owner_type: str, owner_id: str):
        pass

    @abstractmethod
    def get_tenant_by_id(self, owner_type: str, owner_id: str):
        pass


class TbLampController(ABC):
    @staticmethod
    def replace_lamp_qr(request_params, asset_service, device_service, lamp_service):
        pass

    @staticmethod
    def replace_installed_lamp(request_params, asset_service, device_service):
        pass
