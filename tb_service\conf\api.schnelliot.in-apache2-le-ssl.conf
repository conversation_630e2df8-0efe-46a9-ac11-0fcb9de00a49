<IfModule mod_ssl.c>
    <VirtualHost *:443>
    # Server Details
    ServerName SchnellIoTApi
    ServerAlias SchnellIoTApi # api.schnelliot.in
    ServerAdmin <EMAIL>

    # Log Details
    ErrorLog /var/log/schnelliot-api/schnelliot-api-error.log
    CustomLog ${APACHE_LOG_DIR}/schnelliot-api.log combined

    # Project Details
    DocumentRoot /opt/deployment/schnelliot-api/tb_service

    # WSGI Configuration
    WSGIDaemonProcess SchnellIoTApi python-path=/opt/deployment/schnelliot-api/tb_service python-home=/opt/.envs/schnelliot-env
    WSGIProcessGroup SchnellIoTApi
    WSGIScriptAlias / /opt/deployment/schnelliot-api/tb_service/tb_service/wsgi.py

    <Directory /opt/deployment/schnelliot-api/tb_service/tb_service>
        <Files wsgi.py>
            Require all granted
        </Files>
    </Directory>

    # Static Directory
    Alias /static /opt/deployment/schnelliot-api/tb_service/static
    <Directory /opt/deployment/schnelliot-api/tb_service/static>
        Require all granted
    </Directory>

    # Media directory
    Alias /static /opt/deployment/schnelliot-api/tb_service/media
    <Directory /opt/deployment/schnelliot-api/tb_service/media>
        Require all granted
    </Directory>

    # SSL Configuration
    SSLCertificateFile /etc/letsencrypt/live/api.schnelliot.in/fullchain.pem
    SSLCertificateKeyFile /etc/letsencrypt/live/api.schnelliot.in/privkey.pem
    Include /etc/letsencrypt/options-ssl-apache.conf
    </VirtualHost>
</IfModule>
