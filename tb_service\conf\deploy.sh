# If required
# Python3 required else install it
sudo apt update
sudo apt install apache2
sudo apt install libapache2-mod-wsgi-py3
sudo a2enmod wsgi

# Setting Environment
sudo mkdir /opt/.credentials
sudo chmod 777 -R /opt/.credentials

sudo mkdir /opt/.envs
sudo chmod 777 -R /opt/.envs
cd /opt/.envs
sudo apt install python3.12-venv

python3 -m venv schnelliot-env
# sudo chmod 777 -R /opt/.envs/schnelliot-env

# Setting Deployment directory
sudo mkdir /opt/deployment
sudo mkdir /opt/deployment/schnelliot-api

# Setting Code
cd /opt/deployment/schnelliot-api
sudo git clone https://github.com/schnellenergy/schnelliot-api.git .
source /opt/.envs/schnelliot-env/bin/activate
pip install --upgrade pip
pip install -r /opt/deployment/schnelliot-api/tb_service/requirements.txt
deactivate

# Setting Upload directory
sudo mkdir /opt/deployment/schnelliot-api/tb_service/Media
sudo chmod 777 -R /opt/deployment/schnelliot-api/tb_service/Media

# Setting Log directory
sudo mkdir /var/log/schnelliot
sudo chmod 777 -R /var/log/schnelliot

# Place credentials
sudo nano /opt/.credentials/gcp-credentials-schnelliot.json
sudo nano /opt/.credentials/greythr-schnelliot.pem

# Place .env
sudo nano /opt/deployment/schnelliot-api/tb_service/.env

# Place Apache2 configuration
sudo nano /etc/apache2/sites-available/schnelliot-api-9201.conf
sudo a2ensite schnelliot-api-9201.conf
sudo service apache2 restart

# Place HAProxy configuration
sudo nano /etc/haproxy/haproxy.cfg
sudo service haproxy restart

# Prerequisites: HAProxy thingsboard based configuration
# Production - beta domain
sudo certbot-certonly --domain beta.api.schnelliot.in --email <EMAIL>
# Production domain - onboard services /onboard
sudo certbot-certonly --domain onboard.schnelliot.in --email <EMAIL>
# Production domain - custom api service /api
sudo certbot-certonly --domain api.schnelliot.in --email <EMAIL>
sudo haproxy-refresh



sudo certbot-certonly --domain smartlights.in --email <EMAIL>
sudo certbot-certonly --domain eipl.smartlights.in --email <EMAIL>
sudo certbot-certonly --domain spectrum.smartlights.in --email <EMAIL>
sudo certbot-certonly --domain sms.smartlights.in --email <EMAIL>
sudo certbot-certonly --domain kmc.smartlights.in --email <EMAIL>

sudo certbot-certonly --domain tnsmartlights.in --email <EMAIL>

sudo certbot-certonly --domain onboard.schnelliot.in --email <EMAIL>
sudo certbot-certonly --domain beta.api.schnelliot.in --email <EMAIL>
sudo certbot-certonly --domain api.schnelliot.in --email <EMAIL>


