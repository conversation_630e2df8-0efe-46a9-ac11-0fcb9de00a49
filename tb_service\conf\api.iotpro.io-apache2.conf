Listen 8078
WSGISocketPrefix /var/run/wsgi
<VirtualHost *:8078>
    # Server Details
    ServerName api.iotpro.io
    ServerAlias api.iotpro.io
    ServerAdmin <EMAIL>

    # Log Details
    ErrorLog ${APACHE_LOG_DIR}/iotpro-api-error.log
    CustomLog ${APACHE_LOG_DIR}/iotpro-api-access.log combined

    # Project Details
    DocumentRoot /opt/deployment/iotpro-api/tb_service

    # WSGI Configuration
    WSGIApplicationGroup %{GLOBAL}
    WSGIDaemonProcess iotpro-api python-path=/opt/deployment/iotpro-api/tb_service python-home=/opt/.envs/iotpro-env
    WSGIProcessGroup iotpro-api
    WSGIScriptAlias / /opt/deployment/iotpro-api/tb_service/tb_service/wsgi.py

    <Directory /opt/deployment/iotpro-api/tb_service/tb_service>
        <Files wsgi.py>
            Require all granted
        </Files>
    </Directory>
</VirtualHost>
