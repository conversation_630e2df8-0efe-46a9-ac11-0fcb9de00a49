from typing import Any

from botocore.exceptions import NoCredentialsError
from lume.__init__ import *
from tb_service.settings import *
import boto3
import base64


class UtilService:
    def save_image_as_file(self, base_data: str, filename: str) -> bool:
        response = True
        try:
            imgdata = base64.b64decode(base_data)
            with open(filename, 'wb') as f:
                f.write(imgdata)
        except Exception as e:
            response = False
        return response

    def store_image_to_aws(self, bucket_type: str, region_name: str, access_key: str, secret_key: str, bucket_folder: str,
                           file_name: str, file_stored_path: str):
        response = {"status": 200, "message": "Image upload Successfully Completed"}
        try:
            user = boto3.client(bucket_type, region_name=region_name, aws_access_key_id=access_key,
                                aws_secret_access_key=secret_key)
            file_path = bucket_folder + '/' + file_name
            user.upload_file(file_stored_path, AWS_BUCKET_NAME, file_path)
            response["ppe_image_url"] = f"https://{AWS_BUCKET_NAME}.s3.{AWS_BUCKET_REGION}.amazonaws.com/ppeImages/{file_name}"
            os.remove(file_stored_path)
            logger.info({"status": 200, "message": "Image upload Successfully Completed"})
        except Exception as e:
            response = {"status": 400, "message": "Error while upload image"}
        return response

    def upload_image_to_aws(self, request_handler, img_data: str, file_name: str, folder: str):
        response = {}
        try:
            if len(img_data) > 0:
                file_name = str(file_name)
                file_full_path = str(BASE_DIR) + "/" + str(MEDIA_URL) + file_name
                if self.save_image_as_file(base_data=img_data, filename=file_full_path):
                    aws_response = self.store_image_to_aws(bucket_type=AWS_BUCKET_TYPE,
                                                      region_name=AWS_BUCKET_REGION,
                                                      access_key=AWS_BUCKET_ACCESS_KEY,
                                                      secret_key=AWS_BUCKET_SECRET_KEY,
                                                      bucket_folder=folder,
                                                      file_name=file_name,
                                                      file_stored_path=file_full_path)
                    response = aws_response
                    request_handler.update_service_response(aws_response)
                    logger.info("S3: Image upload successful in aws s3 bucket")
                else:
                    request_handler.update_service_response({"status": 1000, "message": "Error while upload image"})
        except Exception as e:
            logger.exception("image couldn't upload to aws s3 bucket %s" % str(e))
        return response

    def generate_presigned_url(self, s3, bucket_name, object_key, expiration=157788000):
        try:
            url = s3.generate_presigned_url(
                'get_object',
                Params={'Bucket': bucket_name, 'Key': object_key},
                ExpiresIn=expiration
            )
            return url
        except NoCredentialsError:
            return "Credentials not available"
