# Schnelliot FastAPI Service

This is a complete conversion of the Django-based Schnelliot API to FastAPI, maintaining 100% compatibility with existing URLs and functionality.

## 🚀 Features

- **Complete Django to FastAPI conversion** with identical URLs and logic
- **Modular structure** with separate modules for lume, onboarder, and smstool
- **Pydantic BaseSettings** for configuration management
- **SQLAlchemy/PyMySQL** for database operations
- **APIRouter** for clean route organization
- **Docker containerization** for easy deployment
- **Comprehensive test suite** using requests package
- **Authentication system** with token validation
- **CORS support** for cross-origin requests
- **Logging middleware** for request tracking

## 📁 Project Structure

```
fastapi_service/
├── main.py                 # FastAPI application entry point
├── config/
│   ├── settings.py         # Pydantic settings configuration
│   └── database.py         # Database connection setup
├── auth/
│   └── dependencies.py     # Authentication dependencies
├── middleware/
│   └── logging_middleware.py  # Request logging middleware
├── lume/
│   ├── router.py          # Lume API routes
│   ├── service.py         # Lume service classes
│   └── patch_views.py     # SL1 patch endpoints
├── onboarder/
│   ├── router.py          # Onboarder API routes
│   └── service.py         # Onboarder service classes
├── utils/
│   └── handler.py         # Request/Response handler
├── tests/
│   └── test_api_endpoints.py  # Comprehensive test suite
├── requirements.txt        # Python dependencies
├── Dockerfile             # Docker configuration
├── docker-compose.yml     # Multi-container setup
└── README.md              # This file
```

## 🛠️ Installation & Setup

### Option 1: Local Development

1. **Clone and navigate to the project:**
   ```bash
   cd fastapi_service
   ```

2. **Create virtual environment:**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables:**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

5. **Run the application:**
   ```bash
   uvicorn main:app --host 0.0.0.0 --port 8000 --reload
   ```

### Option 2: Docker Deployment

1. **Build and run with Docker Compose:**
   ```bash
   docker-compose up --build
   ```

2. **Access the application:**
   - API: http://localhost:8000
   - Health check: http://localhost:8000/health
   - API docs: http://localhost:8000/docs

## 🧪 Testing

### Automated Test Suite

Run the comprehensive test suite that tests all API endpoints:

```bash
python run_tests.py
```

This will:
- Start the FastAPI server (if not running)
- Test all API endpoints with the provided credentials
- Generate a detailed test report (`api_test_results.json`)
- Display pass/fail status for each endpoint

### Manual Testing

You can also test individual endpoints using the provided credentials:
- **Username:** `<EMAIL>`
- **Password:** `sethu@123`

Example login test:
```bash
curl -X POST "http://localhost:8000/api/login/" \
     -H "Content-Type: application/x-www-form-urlencoded" \
     -d "username=<EMAIL>&password=sethu@123"
```

## 📋 API Endpoints

### Authentication
- `POST /api/login/` - User login
- `POST /api/logout/` - User logout
- `POST /api/print_qr/` - Get user role for QR printing

### Device Management
- `POST /api/get/device/` - Get device details
- `POST /api/device/service/` - Device service operations
- `POST /api/ilm/control/` - ILM device control
- `POST /api/gw/control/` - Gateway control

### Entity Operations
- `GET /api/entities/search/` - Search entities
- `GET /api/entity/detail/` - Get entity details
- `GET /api/entities/` - Get entities list

### Device Onboarding
- `GET /onboard/ilm/{credentials}/{variant}/` - Onboard ILM device
- `GET /onboard/gw/{imei_no}/{variant}/` - Onboard Gateway device

### Legacy SL1 APIs
- `POST /api/sms/send/` - SMS sending
- `POST /api/ghmc/tran_ghmc/` - GHMC transactions
- `POST /api/cochin/lamp/failure/` - Lamp failure reporting

## 🔧 Configuration

Key configuration options in `.env`:

```env
# Database
DB_HOST=127.0.0.1
DB_PORT=3307
DB_USER=root
DB_PASSWORD=root
DB_NAME=mysql

# ThingsBoard
BASE_URL=http://iotpro.io
TB_USERNAME=<EMAIL>
TB_PASSWORD=schnell@123

# AWS
AWS_BUCKET_NAME=schnell-s3-image
AWS_BUCKET_REGION=ap-south-1
# ... other AWS settings
```

## 🐳 Docker Support

The project includes full Docker support with:
- **FastAPI application** container
- **MySQL database** container
- **Redis** for caching
- **Nginx** for production deployment

Start everything with:
```bash
docker-compose up -d
```

## 📊 Test Results

The test suite generates comprehensive reports including:
- Individual endpoint test results
- Response status codes and data
- Error messages for failed tests
- Overall success rate
- Detailed timing information

## 🔒 Security

- Token-based authentication
- CORS configuration
- Request validation
- SQL injection protection via parameterized queries

## 📝 Migration Notes

This FastAPI version maintains:
- ✅ **Identical URLs** - No changes to existing API endpoints
- ✅ **Same request/response formats** - Complete compatibility
- ✅ **All business logic** - No functional changes
- ✅ **Database schema** - Uses existing database structure
- ✅ **Authentication flow** - Same token-based auth

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run the test suite
5. Submit a pull request

## 📞 Support

For issues or questions, please check the test results and logs first. The comprehensive test suite should help identify any compatibility issues.
