#!/usr/bin/env python3
"""
Simple test script for the FastAPI endpoints
"""

import requests
import json
import time

def test_endpoints():
    base_url = "http://localhost:8000"
    
    print("🧪 Testing FastAPI Endpoints")
    print("="*50)
    
    # Test 1: Health check
    print("\n1. Testing Health Check...")
    try:
        response = requests.get(f"{base_url}/health")
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.json()}")
    except Exception as e:
        print(f"   Error: {e}")
    
    # Test 2: Root endpoint
    print("\n2. Testing Root Endpoint...")
    try:
        response = requests.get(f"{base_url}/")
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.json()}")
    except Exception as e:
        print(f"   Error: {e}")
    
    # Test 3: Login endpoint
    print("\n3. Testing Login Endpoint...")
    try:
        data = {
            "username": "<EMAIL>",
            "password": "sethu@123",
            "ts": str(int(time.time() * 1000)),
            "latitude": "12.9716",
            "longitude": "77.5946"
        }
        response = requests.post(f"{base_url}/api/login/", data=data)
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.json()}")
        
        # Extract token if login successful
        if response.status_code == 200:
            response_data = response.json()
            if "token" in response_data:
                token = response_data["token"]
                print(f"   Token extracted: {token[:20]}...")
                
                # Test 4: Protected endpoint with token
                print("\n4. Testing Protected Endpoint (Print QR)...")
                try:
                    headers = {"token": token}
                    response = requests.post(f"{base_url}/api/print_qr/", headers=headers)
                    print(f"   Status: {response.status_code}")
                    print(f"   Response: {response.json()}")
                except Exception as e:
                    print(f"   Error: {e}")
                
    except Exception as e:
        print(f"   Error: {e}")
    
    # Test 5: Device onboarding
    print("\n5. Testing Device Onboarding...")
    try:
        response = requests.get(f"{base_url}/onboard/ilm/1234567890123456/SLNNSGINSTDGX01A/")
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text}")
    except Exception as e:
        print(f"   Error: {e}")
    
    # Test 6: Gateway onboarding
    print("\n6. Testing Gateway Onboarding...")
    try:
        response = requests.get(f"{base_url}/onboard/gw/123456789012345/GW_VARIANT/")
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text}")
    except Exception as e:
        print(f"   Error: {e}")
    
    print("\n" + "="*50)
    print("✅ Test completed!")

if __name__ == "__main__":
    test_endpoints()
