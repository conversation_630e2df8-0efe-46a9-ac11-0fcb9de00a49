version: '3.8'

services:
  # FastAPI Application
  fastapi-app:
    build: .
    container_name: schnelliot-fastapi
    ports:
      - "8000:8000"
    environment:
      - DB_HOST=mysql-db
      - DB_PORT=3306
      - DB_USER=root
      - DB_PASSWORD=root
      - DB_NAME=schnelliot_db
    volumes:
      - ./Media:/app/Media
      - ./.env:/app/.env
      - ./gcp-credentials-iotpro.json:/app/gcp-credentials-iotpro.json
    depends_on:
      - mysql-db
    networks:
      - schnelliot-network
    restart: unless-stopped

  # MySQL Database
  mysql-db:
    image: mysql:8.0
    container_name: schnelliot-mysql
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: schnelliot_db
      MYSQL_USER: schnelliot
      MYSQL_PASSWORD: schnelliot123
    ports:
      - "3307:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - schnelliot-network
    restart: unless-stopped

  # Redis (for caching if needed)
  redis:
    image: redis:7-alpine
    container_name: schnelliot-redis
    ports:
      - "6379:6379"
    networks:
      - schnelliot-network
    restart: unless-stopped

  # Nginx (for production deployment)
  nginx:
    image: nginx:alpine
    container_name: schnelliot-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./Media:/var/www/media
    depends_on:
      - fastapi-app
    networks:
      - schnelliot-network
    restart: unless-stopped

volumes:
  mysql_data:

networks:
  schnelliot-network:
    driver: bridge
