"""
FastAPI configuration settings
Converted from Django settings.py using Pydantic BaseSettings
"""

import json
import os
from pathlib import Path
from typing import List
from pydantic import BaseSettings
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

class Settings(BaseSettings):
    # Django Secret
    SECRET_KEY: str = "django-insecure-!2#zh!^271zvb!99ho3fbkw%ddkz0o5@7p%9837uc&dzr!#+"
    
    # Debug mode
    DEBUG: bool = False
    
    # Log configuration
    LOG_FILE_NAME: str = "./schnelliot-api-beta.log"
    LOG_LEVEL: str = "INFO"
    
    # GREY T HR Information
    GREYTHR_HOST: str = "schnellenergy.greythr.com"
    GREYTHR_API_ID: str = "18f4cdb6-9af6-4863-8ff2-509bd18ae0ff"
    GREYTHR_API_ENDPOINT: str = "/v2/attendance/asca/swipes"
    GREYTHR_PEM_FILEPATH: str = "../greythr-schnelliot.pem"
    
    # Database Configuration
    DB_HOST: str = "127.0.0.1"
    DB_PORT: int = 3307
    DB_USER: str = "root"
    DB_NAME: str = "mysql"
    DB_PASSWORD: str = "root"
    
    # Thingsboard Configuration
    BASE_URL: str = "http://iotpro.io"
    TB_USERNAME: str = "<EMAIL>"
    TB_PASSWORD: str = "schnell@123"
    
    # AWS Configuration
    AWS_BUCKET_TYPE: str = "s3"
    AWS_BUCKET_NAME: str = "schnell-s3-image"
    AWS_BUCKET_REGION: str = "ap-south-1"
    AWS_BUCKET_ACCESS_KEY: str = "********************"
    AWS_BUCKET_SECRET_KEY: str = "t99dd9UHF3h4OVvxbosN+JnJIsaD4jd/d+7JNIER"
    AWS_BUCKET_FOLDER_NAME: str = "luminator"
    AWS_BUCKET_PPE_IMAGE_FOLDER_NAME: str = "ppeImages"
    AWS_BUCKET_LUMINATOR_FOLDER_NAME: str = "luminator"
    AWS_BUCKET_GRIEVANCE_FOLDER_NAME: str = "grievances"
    AWS_INSTALL_SERVICE_SQS_URL: str = "https://sqs.ap-south-1.amazonaws.com/************/InstallServicePostProcessQ"
    AWS_INSTALL_SERVICE_REGION: str = "ap-south-1"
    AWS_INSTALL_SERVICE_ACCESS_KEY: str = "********************"
    AWS_INSTALL_SERVICE_SECRET_KEY: str = "6jN6H/rYGh2J3ZqZ5Z9c7dzLAk8cdn/8rIDO791k"
    
    # Google Project information
    SERVICE_ACCOUNT_KEY_PATH: str = "./gcp-credentials-iotpro.json"
    PUBSUB_POLE_TOPIC_PATH: str = "projects/schnelliot-380113/topics/PoleInstallationDataAggregationQ"
    PUBSUB_LAMP_TOPIC_PATH: str = "projects/schnelliot-380113/topics/LampInstallationDataAggregationQ"
    PUBSUB_ACTIVITY_LOG_TOPIC_PATH: str = "projects/schnelliot-380113/topics/ActivityLogDataAggregationQ"
    PUBSUB_POLE_RELOCATION_TOPIC_PATH: str = "projects/schnelliot-380113/topics/poleRelocationQ"
    PUBSUB_IAM_USER_DATA_TOPIC_PATH: str = "projects/iotpro-smartlights/topics/UserCredentials"
    
    # CORS Configuration
    CORS_ALLOWED_ORIGINS: List[str] = [
        "http://iotpro.io",
        "https://iotpro.io",
        "http://schnelliot.in",
        "https://schnelliot.in",
        "http://tbce.iotpro.io:8080",
        "http://prod.schnelliot.in",
        "https://prod.schnelliot.in",
        "http://prod.schnelliot.in:8080",
        "http://192.168.1.206:8080",
        "https://signify.iotpro.io",
        "https://customergrievance-9acd9.web.app"
    ]
    
    CORS_ALLOW_METHODS: List[str] = ['DELETE', 'GET', 'OPTIONS', 'PATCH', 'POST', 'PUT']
    CORS_ALLOW_HEADERS: List[str] = [
        'accept', 'accept-encoding', 'authorization',
        'content-type', 'dnt', 'origin', 'token',
        'user-agent', 'x-csrftoken', 'x-requested-with',
        'Accept-Language', 'Content-Language', 'X-Custom-Header'
    ]
    
    class Config:
        env_file = ".env"
        case_sensitive = True

# Create settings instance
settings = Settings()

# HTTP Headers
HEADER = {'Content-type': 'application/json', 'Accept': '*/*'}

# Login credentials for ThingsBoard
LOGIN_CREDENTIALS = json.dumps({"username": settings.TB_USERNAME, "password": settings.TB_PASSWORD})

# Configure requests session with retry strategy
session = requests.Session()
retry = Retry(total=5, connect=5, backoff_factor=0.5, status_forcelist=[500, 502, 503, 504])
adapter = HTTPAdapter(max_retries=retry, pool_maxsize=100, pool_connections=100)
session.mount('http://', adapter)
session.mount('https://', adapter)

# CSML Data forwarding URLs
CSML_LOGIN_URL = "https://bi.smartkochi.in:8443/auth/realms/schnellenergy/protocol/openid-connect/token"
CSML_LAMP_FAILURE_URL = "https://bi.smartkochi.in:8443/apiman-gateway/FLUENTGRID/Lampfailure/1.0?apikey=1c7b2899-167f-443b-8b33-64885e6cac4a"
kochin_user_data = "username=SchnellEnergyUser&password=sch%24ell@n@r%26yu%24er&grant_type=password&client_id=SchnellEnergy"

# Constants from Django settings
ILM_SERVER_ATTRIBUTES = "testResults,PDI,state,condition,dimmable,active,wardName,zoneName,region,qrCount"
LUME_PRINT_QR_REQUIRED_ROLE = "production manager"
ILM_ATTRIBUTE_TYPE_CONVERT_BOOL = ["dimmable", "PDI", "brightness_100", "brightness_70", "brightness_50",
                                   "brightness_30", "brightness_0", "flash", "rtc", "active"]
ILM_ATTRIBUTE_TYPE_CONVERT_INT = ["qrCount", "error_count", "start_ts", "end_ts", "lastActivityTime", "installedOn",
                                  "lampWatts", "armCount", "lampWattage"]
ILM_ATTRIBUTE_TYPE_CONVERT_DOUBLE = ["latitude", "longitude", "accuracy", "slatitude", "slongitude"]
GW_SERVER_ATTRIBUTES = "active,state,condition,wardName,zoneName,region"
ENTITY_TYPE = ["DEVICE", "ASSET"]
ENTITY_TYPE_ASSET = "ASSET"
ENTITY_TYPE_DEVICE = "DEVICE"
ATTRIBUTE_SCOPES = ["CLIENT_SCOPE", "SERVER_SCOPE", "SHARED_SCOPE"]
DEVICE_TYPES = ("gw", "ilm", "ilm-4g", "ccms", "nic", "smslc")
RELATION_TYPES = ["Contains", "ControlledBy", "LitBy", "Powers", "Routes", "Mounts", "CanAccess"]
ILM_TEST_QUERY_PARAMS = ["jigNumber", "deviceId", "action"]
GW_ENTITY_ID_KEY = ["deviceId"]
ENTITY_RELATIONS = ["FROM", "TO"]
GW_DISPATCH_QUERY_PARAMS = ["panelId", "gwType", "ebMeterNo", "phase", "simNo"]
GW_ASSET_TYPES = ("ccms", "hub", "smslc")
ILM_ASSET_TYPES = ["lamp", "lightPoint", "region", "zone", "ward", "pole", "switchPoint"]

# Installation attributes
LIGHT_POINT_INSTALLATION_ATTRIBUTES = ["latitude", "longitude", "accuracy", "landmark", "wardName", "zoneName",
                                       "region", "installedOn", "installedBy", "lampWatts"]
ILM_INSTALLATION_ATTRIBUTES = ["state", "condition", "landmark", "latitude", "longitude", "zoneName", "region",
                               "wardName", "installedOn", "installedBy"]
CCMS_INSTALLATION_ATTRIBUTES = ["slatitude", "slongitude", "accuracy", "location", "wardName", "zoneName", "region",
                                "installedOn", "installedBy"]
CCMS_EBMETER_ATTRIBUTES = {"ebMeterNo": "name", "meterReadingOffset": "meterReadingOffset",
                           "ebMeterReplacedBy": "replacedBy", "ebMeterReplacedOn": "replacedOn"}
GW_INSTALLATION_ATTRIBUTES = ["state", "condition", "location", "wardName", "zoneName", "region", "installedOn",
                              "installedBy"]
POLE_INSTALLATION_ATTRIBUTES = ["type", "lampProfiles", "name", "discomPoleNumber", "height", "condition", "span", "clampDimension",
                                "vehicleAccessAvailable", "connection", "earthingRequired", "controlWireStatus", "armDetails", "accuracy",
                                "manualSwitchControl", "remarks", "signalStrength", "region", "zoneName", "sector", "wardName", "installedBy", "installedOn",
                                "roadCategory", "roadWidth", "incomingTransmissionLine", "incomingTransmissionType", "bracketMountingHeight",
                                "state", "locationDetails", "latitude", "longitude", "location", "armCount", "auditImg", "assetType", "roadType"]
LAMP_INSTALLATION_ATTRIBUTES = ["lampWatts", "manufacturer", "lampType", "year", "wardName", "installedOn",
                                "installedBy", "dimmable", "state", "condition"]
SWITCH_POINT_INSTALLATION_ATTRIBUTES = ["switchPointNumber", "switchPointType", "panelId", "rrNumber", "meter", "connectedLoad", "roadType", "assetType",
                                        "workingCondition", "earthingCondition", "customerId", "wardId", "switchPointId", "remarks", "region",
                                        "zoneName", "wardName", "installedBy", "installedOn", "state", "locationDetails", "meterDetails",
                                        "latitude", "longitude", "location", "accuracy", "roadCategory", "roadWidth", "vehicleAccess"]

# Other constants
DEVICE_FAILURE_STATUS_HANDLER = [200, 401]
COMMON_PARAMS = "lastActivityTime"
LIGHT_POINT_ASSET_FORMAT = "LP-{asset_name}"
LAMP_ASSET_FORMAT = "LMP-{asset_name}"
AVAILABLE_STATES = ["TESTABLE", "TESTED", "INSTALLABLE", "INSTALLED"]
AVAILABLE_CONDITION = ["NEW", "SERVICE", "SCRAPPED"]
REPLACE_AND_REMOVE_DEVICE_STATE_AND_CONDITION = {"state": "INSTALLABLE"}
LAMP_INSTALLED_STATE_CONDITION = {"state": "INSTALLED", "condition": "NEW"}
INSTALLED_STATE = {"state": "INSTALLED"}
OWNER_TYPE = ["CUSTOMER", "TENANT", "USER"]
OWNER_TYPE_CUSTOMER = "CUSTOMER"
LP_SERVER_ATTRIBUTES = ["accuracy", "lamp"]
DEVICE_FIND_QUERY_ATTRIBUTE_RESPONSE_KEYS = ["state", "condition", "region", "wardName", "zoneName", "location",
                                             "landmark", "latitude", "longitude", "slatitude", "slongitude",
                                             "commissioned", "lastActivityTime", "active", "lampWatts", "accuracy",
                                             "installedOn", "installedBy"]
DEVICE_FIND_QUERY_ENTITY_RESPONSE_KEYS = {"name": "name", "label": "label", "type": "type", "device_id": "id"}
PROJECT_WISE_WATTAGE = "lampWattage"

# AWS file path
AWS_FILE_PATH = f'http://{settings.AWS_BUCKET_NAME}.{settings.AWS_BUCKET_TYPE}.{settings.AWS_BUCKET_REGION}.amazonaws.com'

# Data upload max memory size
DATA_UPLOAD_MAX_MEMORY_SIZE = 10 * 1024 * 1024  # 10 MB
