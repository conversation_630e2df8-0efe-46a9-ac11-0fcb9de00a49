"""
Service classes for onboarder module
Converted from Django service classes
"""

import json
import logging
import requests
from typing import Dict, Any

from config.settings import settings, LOGIN_CREDENTIALS

logger = logging.getLogger(__name__)

tb_url = f"{settings.BASE_URL}/api"

class TBConnection:
    """ThingsBoard connection service"""
    
    @staticmethod
    def get_tb_token(tb_user_data: str = '') -> Dict[str, Any]:
        """Get ThingsBoard authentication token"""
        try:
            # For FastAPI, we don't have sessions, so always get a new token
            for i in range(2):
                try:
                    url = tb_url + "/auth/login"
                    header = {'Content-Type': 'application/json', 'Accept': 'application/json'}
                    tb_get_token_response = requests.post(url, headers=header, data=tb_user_data, timeout=60)
                    tb_token_json_data = json.loads(tb_get_token_response.text)
                    tb_auth_token = tb_token_json_data.get('token')
                    tb_refresh_token = tb_token_json_data.get('refreshToken')
                    logger.info("TB Token obtained successfully")
                    return {'token': tb_auth_token, 'refreshToken': tb_refresh_token}
                except Exception as e:
                    logger.exception("get_tb_token attempt %d failed: %s" % (i+1, str(e)))
                    if i == 1:  # Last attempt
                        raise e
        except Exception as e:
            logger.exception("get_tb_token failed: %s" % str(e))
            return {'error': str(e)}

    @staticmethod
    def refresh_tb_token(tb_api_url: str, url_method: str, tb_api_default_url: str, 
                        tb_user_data: str, tb_api_data: str) -> requests.Response:
        """Refresh ThingsBoard token and retry the failed request"""
        try:
            url = tb_api_default_url + "/auth/login"
            header = {'Content-Type': 'application/json', 'Accept': 'application/json'}
            tb_get_token_response = requests.post(url, headers=header, data=tb_user_data, timeout=60)
            logger.info("refresh_tb_token response: %s" % tb_get_token_response.status_code)
            tb_token_json_data = json.loads(tb_get_token_response.text)
            tb_auth_token = tb_token_json_data.get('token')
            
            # Retry the failed request with new token
            new_header = {'Content-type': 'application/json', 'Accept': '*/*',
                         'X-Authorization': 'Bearer ' + tb_auth_token}
            
            if url_method.lower() == 'get':
                return requests.get(tb_api_url, headers=new_header, data=tb_api_data, timeout=60)
            elif url_method.lower() == 'post':
                return requests.post(tb_api_url, headers=new_header, data=tb_api_data, timeout=60)
            else:
                raise ValueError(f"Unsupported HTTP method: {url_method}")
                
        except Exception as e:
            logger.exception("refresh_tb_token failed: %s" % str(e))
            raise e

    @staticmethod
    def tb_logout_token(header: Dict[str, str] = None) -> str:
        """Logout from ThingsBoard"""
        try:
            url = tb_url + "/auth/logout"
            tb_get_token_response = requests.post(url, headers=header, data='', timeout=60)
            logger.info("tb_logout_response status: %s" % tb_get_token_response.status_code)
            return 'ok'
        except Exception as e:
            logger.exception("tb_logout_Error: %s" % str(e))
            return 'fail'
